package creative

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AutoPublishVideoRouter struct{}

func (r *AutoPublishVideoRouter) InitAutoPublishVideoRouter(Router *gin.RouterGroup) {
	autoPublishVideoRouter := Router.Group("auto-publish-video").Use(middleware.OperationRecord())
	var autoPublishVideoApi = v1.ApiGroupApp.CreativeApiGroup.AutoPublishVideoApi
	{
		autoPublishVideoRouter.GET("list", autoPublishVideoApi.List)                           // 获取自动发布记录
		autoPublishVideoRouter.POST("save", autoPublishVideoApi.Save)                          // 保存发布记录
		autoPublishVideoRouter.POST("change-status", autoPublishVideoApi.ChangeStatus)         // 更改发布状态
		autoPublishVideoRouter.GET("log-list", autoPublishVideoApi.GetAutoPublishVideoLogList) // 获取自动发布日志
		autoPublishVideoRouter.POST("re-publish", autoPublishVideoApi.RePublish)               // 重新发布视频
	}
}
