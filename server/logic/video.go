package logic

import (
	"context"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
)

// 视频逻辑处理
type VideoLogic struct{}

// 上传视频
func (l *VideoLogic) UploadVideo(uniqueId string, video *creative.Video, record *creative.AutoPublishVideoRecord) (err error) {
	record.VideoId = video.ID
	record.Title = video.Title

	// 获取商品信息，判断是否为带货视频
	var productInfo *douyin.DyProductManual
	if video.ManualProductId > 0 {
		err := global.GVA_DB.First(&productInfo, video.ManualProductId).Error
		if err != nil {
			return fmt.Errorf("获取商品信息失败: %v", err.Error())
		}
	}

	// 如果有商品信息且推广链接不为空，则校验用户的web cookie是否存在
	if productInfo != nil && productInfo.PromotionUrl != "" {
		fmt.Printf("准备上传带货视频:%s", uniqueId)
		redisKey := fmt.Sprintf(douyin.DyWebCookieKey, uniqueId)
		webCookie, WebCookieErr := global.GVA_REDIS.Get(context.Background(), redisKey).Result()
		if WebCookieErr != nil || webCookie == "" {
			record.Status = 5
			record.Reason = "该用户无法上传带货视频, 请先进行营销授权"
			global.GVA_DB.Create(&record)
			return fmt.Errorf("该用户无法上传带货视频, 请先进行营销授权")
		}
	}

	cookie, err := service.ServiceGroupApp.DouyinServiceGroup.DyUserService.GetAwemeCookie(record.UniqueId)
	if err != nil {
		return fmt.Errorf("获取抖音用户cookie失败,请重新登录: %v", err.Error())
	}

	videoService := service.ServiceGroupApp.CreativeServiceGroup.VideoService

	// 1. 调用视频上传接口
	uploadReq := request.MoreCreatorApiVodUploadRequest{
		VideoPath: video.Url,
		Cookie:    cookie,
	}
	user, err := service.ServiceGroupApp.DouyinServiceGroup.DyUserService.GetUserByID(uint(record.DyUserId))
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err.Error())
	}
	uploadReq.Proxy = user.BindIP
	resp, err := service.ServiceGroupApp.DouyinServiceGroup.MoreCreatorApiService.VodUpload(uploadReq)
	if err != nil {
		_ = videoService.UpdateVideoStatus(record.VideoId, 3)
		record.Status = 5
		record.Reason = fmt.Sprintf("请求上传失败:record_id:%d, video_url:%s, code:%d, status_msg:%s", record.ID, video.Url, resp.Code, resp.Msg)
		return fmt.Errorf("%s", record.Reason)
	}

	if resp.Data.TaskId == "" {
		_ = videoService.UpdateVideoStatus(record.VideoId, 3)
		record.Status = 5
		record.Reason = fmt.Sprintf("上传失败:record_id:%d, video_url:%s, 未获得taskId: status_code:%d, status_msg:%s", record.ID, video.Url, resp.Data.StatusCode, resp.Data.StatusMsg)
		return fmt.Errorf("%s", record.Reason)
	}

	// 记录任务id
	record.Status = 1
	record.TaskId = resp.Data.TaskId

	// 从数据库中获取发布记录，如果存在则更新，否则创建新记录
	var oldRecord creative.AutoPublishVideoRecord
	global.GVA_DB.Where("id = ?", record.ID).First(&oldRecord)
	var saveErr error
	if oldRecord.ID == 0 {
		saveErr = global.GVA_DB.Create(&record).Error
	} else {
		saveErr = global.GVA_DB.Model(&record).Updates(&record).Error
	}
	if saveErr != nil {
		err = saveErr
		return
	}

	// 修改素材状态为被选用
	err = service.ServiceGroupApp.CreativeServiceGroup.VideoService.UpdateVideoStatus(record.VideoId, 4)
	return
}
