package creative

import (
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	creativeReq "github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
)

type VideoCategoryService struct{}

// CreateVideoCategory 创建视频分类
func (s *VideoCategoryService) CreateVideoCategory(category *creative.VideoCategory) error {
	// 如果 a.ParentId 为 nil, 则将其设置为 NULL
	if category.ParentId != nil && *category.ParentId == 0 {
		category.ParentId = nil
	}
	return global.GVA_DB.Create(category).Error
}

// DeleteVideoCategory 删除视频分类
func (s *VideoCategoryService) DeleteVideoCategory(id uint) error {
	// 检查是否有子分类
	var count int64
	err := global.GVA_DB.Model(&creative.VideoCategory{}).Where("parent_id = ?", id).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("此分类下有子分类，不能删除")
	}
	return global.GVA_DB.Delete(&creative.VideoCategory{}, id).Error
}

// UpdateVideoCategory 更新视频分类
func (s *VideoCategoryService) UpdateVideoCategory(category *creative.VideoCategory) error {
	updateMap := map[string]interface{}{
		"name":      category.Name,
		"sort":      category.Sort,
		"parent_id": category.ParentId,
	}
	if category.Status != nil {
		updateMap["status"] = *category.Status
	}
	return global.GVA_DB.Model(&creative.VideoCategory{}).Where("id = ?", category.ID).Updates(updateMap).Error
}

// SetVideoCategoryStatus 设置视频分类状态
func (s *VideoCategoryService) SetVideoCategoryStatus(id uint, status int) error {
	return global.GVA_DB.Model(&creative.VideoCategory{}).Where("id = ?", id).Update("status", status).Error
}

// GetVideoCategory 根据id获取视频分类
func (s *VideoCategoryService) GetVideoCategory(id uint) (category creative.VideoCategory, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&category).Error
	return
}

// GetVideoCategoryList 获取视频分类列表
func (s *VideoCategoryService) GetVideoCategoryList(
	info creativeReq.VideoCategorySearch, userIds []uint,
) (list []creative.VideoCategory, total int64, err error) {
	if info.Page == 0 {
		info.Page = 1
	}
	if info.PageSize == 0 {
		info.PageSize = 100
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&creative.VideoCategory{})
	db = db.Where("created_by IN (?)", userIds)

	// 只查询一级分类
	db = db.Where("parent_id IS NULL")

	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Order("sort desc,id desc").Limit(limit).Offset(offset).Find(&list).Error
	if err != nil {
		return
	}

	// 提取一级分类的ID
	var parentIds []uint
	for _, category := range list {
		parentIds = append(parentIds, category.ID)
	}

	// 查询二级分类
	if len(parentIds) > 0 {
		var children []creative.VideoCategory
		childDb := global.GVA_DB.Model(&creative.VideoCategory{})
		childDb = childDb.Where("parent_id IN (?)", parentIds)
		err = childDb.Order("sort desc, id desc").Find(&children).Error
		if err != nil {
			return
		}

		// 将二级分类分组
		childrenMap := make(map[uint][]creative.VideoCategory)
		for _, child := range children {
			if child.ParentId != nil {
				childrenMap[*child.ParentId] = append(childrenMap[*child.ParentId], child)
			}
		}

		// 将二级分类附加到一级分类
		for i := range list {
			list[i].Children = childrenMap[list[i].ID]
		}
	}

	// 拼接所有分类进行后续处理
	allCategories := make([]*creative.VideoCategory, 0)
	for i := range list {
		allCategories = append(allCategories, &list[i])
		for j := range list[i].Children {
			allCategories = append(allCategories, &list[i].Children[j])
		}
	}

	// 批量获取创建人信息，避免N+1查询
	creatorIds := make([]uint, 0)
	categoryIds := make([]uint, 0)
	for _, category := range allCategories {
		if category.CreatedBy > 0 {
			creatorIds = append(creatorIds, category.CreatedBy)
		}
		categoryIds = append(categoryIds, category.ID)
	}

	// 批量查询创建人信息
	creatorMap := make(map[uint]string)
	if len(creatorIds) > 0 {
		var creators []struct {
			ID       uint   `json:"id"`
			NickName string `json:"nick_name"`
		}
		err := global.GVA_DB.Table("sys_users").
			Select("id, nick_name").
			Where("id IN (?)", creatorIds).
			Find(&creators).Error
		if err == nil {
			for _, creator := range creators {
				creatorMap[creator.ID] = creator.NickName
			}
		}
	}

	// 批量查询视频数量
	videoCountMap := make(map[uint]int64)
	pendingVideoCountMap := make(map[uint]int64)
	if len(categoryIds) > 0 {
		// 查询所有视频数量
		var videoCounts []struct {
			CategoryId uint  `json:"category_id"`
			Count      int64 `json:"count"`
		}
		err := global.GVA_DB.Model(&creative.Video{}).
			Select("category_id, COUNT(*) as count").
			Where("category_id IN (?)", categoryIds).
			Group("category_id").
			Find(&videoCounts).Error
		if err == nil {
			for _, vc := range videoCounts {
				videoCountMap[vc.CategoryId] = vc.Count
			}
		}

		// 查询待发布视频数量
		var pendingVideoCounts []struct {
			CategoryId uint  `json:"category_id"`
			Count      int64 `json:"count"`
		}
		err = global.GVA_DB.Model(&creative.Video{}).
			Select("category_id, COUNT(*) as count").
			Where("category_id IN (?) AND status = ?", categoryIds, creative.VideoStatusPending).
			Group("category_id").
			Find(&pendingVideoCounts).Error
		if err == nil {
			for _, pvc := range pendingVideoCounts {
				pendingVideoCountMap[pvc.CategoryId] = pvc.Count
			}
		}
	}

	// 设置分类的创建人昵称和视频数量
	for _, category := range allCategories {
		if category.CreatedBy > 0 {
			category.CreatorName = creatorMap[category.CreatedBy]
		}
		category.VideoCount = videoCountMap[category.ID]
		category.PendingVideoCount = pendingVideoCountMap[category.ID]
	}
	return
}
