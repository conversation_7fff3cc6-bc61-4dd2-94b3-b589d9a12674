package creative

import (
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"gorm.io/gorm"
)

type VideoService struct{}

// CreateVideo 创建视频
func (s *VideoService) CreateVideo(video *creative.Video) error {
	if err := global.GVA_DB.Create(video).Error; err != nil {
		return err
	}
	return nil
}

// DeleteVideo 删除视频
func (s *VideoService) DeleteVideo(id uint) error {
	var video creative.Video
	if err := global.GVA_DB.Where("id = ?", id).First(&video).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("视频不存在")
		}
		return err
	}

	if err := global.GVA_DB.Delete(&video).Error; err != nil {
		return err
	}
	return nil
}

// UpdateVideo 更新视频
func (s *VideoService) UpdateVideo(video *creative.Video) error {
	var oldVideo creative.Video
	if err := global.GVA_DB.Where("id = ?", video.ID).First(&oldVideo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("视频不存在")
		}
		return err
	}

	if err := global.GVA_DB.Model(&oldVideo).Updates(map[string]interface{}{
		"title":             video.Title,
		"category_id":       video.CategoryId,
		"cover":             video.Cover,
		"status":            video.Status,
		"topic":             video.Topic,
		"url":               video.Url,
		"manual_product_id": video.ManualProductId,
		"duration":          video.Duration,
	}).Error; err != nil {
		return err
	}
	return nil
}

// SetCover 设置封面
func (s *VideoService) SetCover(id uint, cover string) error {
	var video creative.Video
	if err := global.GVA_DB.Where("id = ?", id).First(&video).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("视频不存在")
		}
		return err
	}

	if err := global.GVA_DB.Model(&video).Update("cover", cover).Error; err != nil {
		return err
	}
	return nil
}

// GetVideoList 获取视频列表
func (s *VideoService) GetVideoList(
	info request.VideoSearch, userIds []uint,
) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&creative.Video{})
	db = db.Where("created_by IN (?)", userIds)
	var videos []creative.Video

	// 条件查询
	if info.Title != "" {
		db = db.Where("title LIKE ?", "%"+info.Title+"%")
	}
	if info.CategoryId != nil {
		db = db.Where("category_id = ?", *info.CategoryId)
	}
	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	if info.HasPromotion != nil {
		if *info.HasPromotion {
			// 有带货信息：manual_product_id > 0
			db = db.Where("manual_product_id > 0")
		} else {
			// 无带货信息：manual_product_id = 0 或 NULL
			db = db.Where("manual_product_id = 0 OR manual_product_id IS NULL")
		}
	}

	// 计算总数
	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 查询数据
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&videos).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取视频列表
	var responseList []response.VideoResponse
	for _, video := range videos {
		// 获取分类名称
		var category creative.VideoCategory
		if err := global.GVA_DB.Where("id = ?", video.CategoryId).First(&category).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, 0, err
			}
		}

		// 获取关联的商品信息
		var manualProduct *douyin.DyProductManual
		if video.ManualProductId > 0 {
			var product douyin.DyProductManual
			if err := global.GVA_DB.Where("id = ?", video.ManualProductId).First(&product).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					return nil, 0, err
				}
			} else {
				manualProduct = &product
			}
		}

		responseList = append(responseList, response.VideoResponse{
			ID:              video.ID,
			CategoryId:      video.CategoryId,
			CategoryName:    category.Name,
			Title:           video.Title,
			Cover:           video.Cover,
			Url:             video.Url,
			Status:          video.Status,
			CreatedAt:       video.CreatedAt,
			Source:          video.Source,
			Topic:           video.Topic,
			ManualProductId: video.ManualProductId,
			ManualProduct:   manualProduct,
			Duration:        video.Duration,
		})
	}

	return responseList, total, nil
}

// GetVideo 获取视频详情
func (s *VideoService) GetVideo(id uint) (video *creative.Video, err error) {
	var result creative.Video
	if err := global.GVA_DB.Where("id = ?", id).First(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("视频不存在")
		}
		return nil, err
	}
	return &result, nil
}

// GetVideoByCategoryId 根据分类ID获取一条视频
func (s *VideoService) GetVideoByCategoryId(categoryId uint) (video *creative.Video, err error) {
	var result creative.Video
	if err := global.GVA_DB.Where("category_id = ? And Status = ? And Url IS NOT NULL", categoryId, 0).First(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("该分类下没有视频")
		}
		return nil, err
	}
	return &result, nil
}

// GetVideoByPostId 获取视频
func (s *VideoService) GetVideoByPostId(postId uint) (video *creative.Video, err error) {
	var result creative.Video
	if err := global.GVA_DB.Where("post_id = ?", postId).First(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("视频不存在")
		}
		return nil, err
	}
	return &result, nil
}

// UpdateVideoStatus 更新视频状态
func (s *VideoService) UpdateVideoStatus(id uint, status int) (err error) {
	var video creative.Video
	if err := global.GVA_DB.Where("id =?", id).First(&video).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("视频不存在")
		}
	}
	if err := global.GVA_DB.Model(&video).Update("status", status).Error; err != nil {
		return err
	}
	return nil
}
