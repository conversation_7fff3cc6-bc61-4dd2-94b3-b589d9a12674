package creative

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative/request"
)

type AutoPublishVideoRecordService struct{}

// Create 创建发布记录
func (s *AutoPublishVideoRecordService) Create(record *creative.AutoPublishVideoRecord) error {
	return global.GVA_DB.Create(record).Error
}

func (s *AutoPublishVideoRecordService) Update(record *creative.AutoPublishVideoRecord) error {
	return global.GVA_DB.Model(record).Updates(record).Error
}

// Fail 发布失败
func (s *AutoPublishVideoRecordService) Fail(record *creative.AutoPublishVideoRecord, reason string) error {
	record.Status = 4
	record.Reason = reason
	return s.Update(record)
}

// 发布成功
func (s *AutoPublishVideoRecordService) Success(record *creative.AutoPublishVideoRecord) error {
	record.Status = 3
	record.Reason = ""
	return s.Update(record)
}

// 分页查询自动发布视频日志
func (s *AutoPublishVideoRecordService) GetAutoPublishVideoLogPageList(info request.GetAutoPublishVideoLogSearch) (list []creative.AutoPublishVideoRecord, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&creative.AutoPublishVideoRecord{})
	if len(info.SysUserIds) > 0 {
		db = db.Where("sys_user_id in ?", info.SysUserIds)
	}
	if info.Status != 0 {
		db = db.Where("status =?", info.Status)
	}
	if info.UniqueId != "" {
		db = db.Where("unique_id =?", info.UniqueId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("id DESC").Find(&list).Error
	return
}
