package system

import (
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: Register
//@description: 用户注册
//@param: u model.SysUser, creatorID uint
//@return: userInter system.SysUser, err error

type UserService struct{}

var UserServiceApp = new(UserService)

func (userService *UserService) Register(u system.SysUser, creatorID uint) (userInter system.SysUser, err error) {
	var user system.SysUser
	if !errors.Is(global.GVA_DB.Where("username = ?", u.Username).First(&user).Error, gorm.ErrRecordNotFound) { // 判断用户名是否注册
		return userInter, errors.New("用户名已注册")
	}
	// 否则 附加uuid 密码hash加密 注册
	u.Password = utils.BcryptHash(u.Password)
	u.UUID = uuid.New()
	u.CreatedBy = creatorID // 使用传入的创建者ID
	err = global.GVA_DB.Create(&u).Error
	return u, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: Login
//@description: 用户登录
//@param: u *model.SysUser
//@return: err error, userInter *model.SysUser

func (userService *UserService) Login(u *system.SysUser) (userInter *system.SysUser, err error) {
	if nil == global.GVA_DB {
		return nil, fmt.Errorf("db not init")
	}

	var user system.SysUser
	err = global.GVA_DB.Where("username = ?", u.Username).Preload("Authorities").Preload("Authority").First(&user).Error
	if err == nil {
		if ok := utils.BcryptCheck(u.Password, user.Password); !ok {
			return nil, errors.New("密码错误")
		}
		MenuServiceApp.UserAuthorityDefaultRouter(&user)
	}
	return &user, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: ChangePassword
//@description: 修改用户密码
//@param: u *model.SysUser, newPassword string
//@return: userInter *model.SysUser,err error

func (userService *UserService) ChangePassword(u *system.SysUser, newPassword string) (userInter *system.SysUser, err error) {
	var user system.SysUser
	if err = global.GVA_DB.Where("id = ?", u.ID).First(&user).Error; err != nil {
		return nil, err
	}
	if ok := utils.BcryptCheck(u.Password, user.Password); !ok {
		return nil, errors.New("原密码错误")
	}
	user.Password = utils.BcryptHash(newPassword)
	err = global.GVA_DB.Save(&user).Error
	return &user, err

}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetUserInfoList
//@description: 分页获取数据
//@param: info request.PageInfo
//@return: err error, list interface{}, total int64

func (userService *UserService) GetUserInfoList(info systemReq.GetUserList) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&system.SysUser{})
	var userList []system.SysUser

	if info.NickName != "" {
		db = db.Where("nick_name LIKE ?", "%"+info.NickName+"%")
	}
	if info.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+info.Phone+"%")
	}
	if info.Username != "" {
		db = db.Where("username LIKE ?", "%"+info.Username+"%")
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Offset(offset).Preload("Authorities").Preload("Authority").Find(&userList).Error
	return userList, total, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetUserAuthority
//@description: 设置一个用户的权限
//@param: uuid uuid.UUID, authorityId string
//@return: err error

func (userService *UserService) SetUserAuthority(id uint, authorityId uint) (err error) {

	assignErr := global.GVA_DB.Where("sys_user_id = ? AND sys_authority_authority_id = ?", id, authorityId).First(&system.SysUserAuthority{}).Error
	if errors.Is(assignErr, gorm.ErrRecordNotFound) {
		return errors.New("该用户无此角色")
	}

	var authority system.SysAuthority
	err = global.GVA_DB.Where("authority_id = ?", authorityId).First(&authority).Error
	if err != nil {
		return err
	}
	var authorityMenu []system.SysAuthorityMenu
	var authorityMenuIDs []string
	err = global.GVA_DB.Where("sys_authority_authority_id = ?", authorityId).Find(&authorityMenu).Error
	if err != nil {
		return err
	}

	for i := range authorityMenu {
		authorityMenuIDs = append(authorityMenuIDs, authorityMenu[i].MenuId)
	}

	var authorityMenus []system.SysBaseMenu
	err = global.GVA_DB.Preload("Parameters").Where("id in (?)", authorityMenuIDs).Find(&authorityMenus).Error
	if err != nil {
		return err
	}
	hasMenu := false
	for i := range authorityMenus {
		if authorityMenus[i].Name == authority.DefaultRouter {
			hasMenu = true
			break
		}
	}
	if !hasMenu {
		return errors.New("找不到默认路由,无法切换本角色")
	}

	err = global.GVA_DB.Model(&system.SysUser{}).Where("id = ?", id).Update("authority_id", authorityId).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetUserAuthorities
//@description: 设置一个用户的权限
//@param: id uint, authorityIds []string
//@return: err error

func (userService *UserService) SetUserAuthorities(adminAuthorityID, id uint, authorityIds []uint) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var user system.SysUser
		TxErr := tx.Where("id = ?", id).First(&user).Error
		if TxErr != nil {
			global.GVA_LOG.Debug(TxErr.Error())
			return errors.New("查询用户数据失败")
		}
		TxErr = tx.Delete(&[]system.SysUserAuthority{}, "sys_user_id = ?", id).Error
		if TxErr != nil {
			return TxErr
		}
		var useAuthority []system.SysUserAuthority
		for _, v := range authorityIds {
			e := AuthorityServiceApp.CheckAuthorityIDAuth(adminAuthorityID, v)
			if e != nil {
				return e
			}
			useAuthority = append(useAuthority, system.SysUserAuthority{
				SysUserId: id, SysAuthorityAuthorityId: v,
			})
		}
		TxErr = tx.Create(&useAuthority).Error
		if TxErr != nil {
			return TxErr
		}
		TxErr = tx.Model(&user).Update("authority_id", authorityIds[0]).Error
		if TxErr != nil {
			return TxErr
		}
		// 返回 nil 提交事务
		return nil
	})
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteUser
//@description: 删除用户
//@param: id float64
//@return: err error

func (userService *UserService) DeleteUser(id int) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id = ?", id).Delete(&system.SysUser{}).Error; err != nil {
			return err
		}
		if err := tx.Delete(&[]system.SysUserAuthority{}, "sys_user_id = ?", id).Error; err != nil {
			return err
		}
		return nil
	})
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetUserInfo
//@description: 设置用户信息
//@param: reqUser model.SysUser
//@return: err error, user model.SysUser

func (userService *UserService) SetUserInfo(req system.SysUser) error {
	return global.GVA_DB.Model(&system.SysUser{}).
		Select("updated_at", "nick_name", "header_img", "phone", "email", "enable").
		Where("id=?", req.ID).
		Updates(map[string]interface{}{
			"updated_at": time.Now(),
			"nick_name":  req.NickName,
			"header_img": req.HeaderImg,
			"phone":      req.Phone,
			"email":      req.Email,
			"enable":     req.Enable,
		}).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetSelfInfo
//@description: 设置用户信息
//@param: reqUser model.SysUser
//@return: err error, user model.SysUser

func (userService *UserService) SetSelfInfo(req system.SysUser) error {
	return global.GVA_DB.Model(&system.SysUser{}).
		Where("id=?", req.ID).
		Updates(req).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetSelfSetting
//@description: 设置用户配置
//@param: req datatypes.JSON, uid uint
//@return: err error

func (userService *UserService) SetSelfSetting(req common.JSONMap, uid uint) error {
	return global.GVA_DB.Model(&system.SysUser{}).Where("id = ?", uid).Update("origin_setting", req).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: GetUserInfo
//@description: 获取用户信息
//@param: uuid uuid.UUID
//@return: err error, user system.SysUser

func (userService *UserService) GetUserInfo(uuid uuid.UUID) (user system.SysUser, err error) {
	var reqUser system.SysUser
	err = global.GVA_DB.Preload("Authorities").Preload("Authority").First(&reqUser, "uuid = ?", uuid).Error
	if err != nil {
		return reqUser, err
	}
	MenuServiceApp.UserAuthorityDefaultRouter(&reqUser)
	return reqUser, err
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: FindUserById
//@description: 通过id获取用户信息
//@param: id int
//@return: err error, user *model.SysUser

func (userService *UserService) FindUserById(id int) (user *system.SysUser, err error) {
	var u system.SysUser
	err = global.GVA_DB.Where("id = ?", id).First(&u).Error
	return &u, err
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: FindUserByUuid
//@description: 通过uuid获取用户信息
//@param: uuid string
//@return: err error, user *model.SysUser

func (userService *UserService) FindUserByUuid(uuid string) (user *system.SysUser, err error) {
	var u system.SysUser
	if err = global.GVA_DB.Where("uuid = ?", uuid).First(&u).Error; err != nil {
		return &u, errors.New("用户不存在")
	}
	return &u, nil
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: ResetPassword
//@description: 修改用户密码
//@param: ID uint
//@return: err error

func (userService *UserService) ResetPassword(ID uint) (err error) {
	err = global.GVA_DB.Model(&system.SysUser{}).Where("id = ?", ID).Update("password", utils.BcryptHash("123456")).Error
	return err
}

// GetUserById 根据用户ID获取用户信息
func (userService *UserService) GetUserById(id uint) (*system.SysUser, error) {
	var user system.SysUser
	err := global.GVA_DB.Preload("Authorities").First(&user, id).Error
	return &user, err
}

// GetChildrenAuthorityId 获取子角色ID列表
func (userService *UserService) GetChildrenAuthorityId(authorityId uint) ([]uint, error) {
	var children []system.SysAuthority
	err := global.GVA_DB.Where("parent_id = ?", authorityId).Find(&children).Error
	if err != nil {
		return nil, err
	}

	childrenIds := make([]uint, 0)
	for _, child := range children {
		childrenIds = append(childrenIds, child.AuthorityId)
		// 递归获取子角色的子角色
		subChildren, err := userService.GetChildrenAuthorityId(child.AuthorityId)
		if err != nil {
			return nil, err
		}
		childrenIds = append(childrenIds, subChildren...)
	}
	return childrenIds, nil
}

// GetUserInfoListByAuthorityIds 根据角色ID列表获取用户列表
func (userService *UserService) GetUserInfoListByAuthorityIds(info systemReq.GetUserList, authorityIds []uint) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&system.SysUser{})
	var userList []system.SysUser

	// 构建查询条件：使用 authority_id IN 查询
	subQuery := global.GVA_DB.Table("sys_user_authority").
		Select("sys_user_id").
		Where("sys_authority_authority_id IN ?", authorityIds)

	// 查询在子角色中的用户
	db = db.Where("id IN (?)", subQuery)

	// 添加其他查询条件
	if info.Username != "" {
		db = db.Where("username LIKE ?", "%"+info.Username+"%")
	}
	if info.NickName != "" {
		db = db.Where("nick_name LIKE ?", "%"+info.NickName+"%")
	}
	if info.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+info.Phone+"%")
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Limit(limit).Offset(offset).Preload("Authorities").Preload("Authority").Find(&userList).Error

	return userList, total, err
}

// GetSubordinateUserInfoList 获取下级用户列表，包含创建者信息
func (userService *UserService) GetUserInfoListWithCreator(info systemReq.GetUserList, currentUserId uint) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&system.SysUser{})
	var userList []system.SysUser

	// 获取当前用户的所有下级用户ID
	subordinateUserIds, err := userService.GetSubordinateUserIds(currentUserId)
	if err != nil {
		return nil, 0, err
	}

	// 只查询下级用户，不包含当前用户
	if len(subordinateUserIds) > 0 {
		db = db.Where("id IN ?", subordinateUserIds)
	} else {
		// 如果没有下级用户，返回空列表
		return []system.SysUser{}, 0, nil
	}

	// 添加模糊搜索条件
	if info.Username != "" {
		db = db.Where("username LIKE ?", "%"+info.Username+"%")
	}
	if info.NickName != "" {
		db = db.Where("nick_name LIKE ?", "%"+info.NickName+"%")
	}
	if info.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+info.Phone+"%")
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取用户列表
	err = db.Limit(limit).Offset(offset).
		Preload("Authorities").
		Preload("Authority").
		Find(&userList).Error

	if err != nil {
		return nil, 0, err
	}

	// 手动获取创建者信息
	type UserWithCreator struct {
		system.SysUser
		CreatorName string `json:"creatorName"`
	}

	var result []UserWithCreator

	// 收集所有创建者ID
	creatorIds := make([]uint, 0)
	for _, user := range userList {
		if user.CreatedBy > 0 {
			creatorIds = append(creatorIds, user.CreatedBy)
		}
	}

	// 如果有创建者ID，查询创建者信息
	creatorMap := make(map[uint]string)
	if len(creatorIds) > 0 {
		var creators []system.SysUser
		if err := global.GVA_DB.Where("id IN ?", creatorIds).Select("id, nick_name").Find(&creators).Error; err != nil {
			return nil, 0, err
		}

		// 创建创建者ID到昵称的映射
		for _, creator := range creators {
			creatorMap[creator.ID] = creator.NickName
		}
	}

	// 组装结果
	for _, user := range userList {
		userWithCreator := UserWithCreator{
			SysUser:     user,
			CreatorName: creatorMap[user.CreatedBy],
		}
		result = append(result, userWithCreator)
	}

	return result, total, nil
}

// GetSubordinateUserIds 递归获取所有下级用户ID
func (userService *UserService) GetSubordinateUserIds(userId uint) ([]uint, error) {
	// 一次性获取所有用户的ID和created_by信息，避免N+1查询
	var allUsers []struct {
		ID        uint `json:"id"`
		CreatedBy uint `json:"created_by"`
	}

	err := global.GVA_DB.Model(&system.SysUser{}).
		Select("id, created_by").
		Find(&allUsers).Error
	if err != nil {
		return nil, err
	}

	// 构建父子关系映射表
	childrenMap := make(map[uint][]uint)
	for _, user := range allUsers {
		if user.CreatedBy > 0 {
			childrenMap[user.CreatedBy] = append(childrenMap[user.CreatedBy], user.ID)
		}
	}

	// 递归获取所有下级用户ID
	var result []uint
	var findSubordinates func(uint)
	findSubordinates = func(parentId uint) {
		if children, exists := childrenMap[parentId]; exists {
			for _, childId := range children {
				result = append(result, childId)
				findSubordinates(childId) // 递归获取子用户的下级
			}
		}
	}

	findSubordinates(userId)
	return result, nil
}

// GetTopLevelUser 获取当前用户的最上级用户（系统管理员的下一级用户）
func (userService *UserService) GetTopLevelUser(userId uint) (uint, error) {
	// 如果是系统管理员，返回自己
	if userId == 1 {
		return userId, nil
	}

	var user system.SysUser
	err := global.GVA_DB.First(&user, userId).Error
	if err != nil {
		return 0, err
	}

	// 如果created_by为1，说明当前用户就是最上级用户
	if user.CreatedBy == 1 {
		return userId, nil
	}

	// 递归查找最上级用户
	return userService.GetTopLevelUser(user.CreatedBy)
}

// GetTopLevelUserAndAllSubordinates 获取最上级用户及其下的所有用户ID
func (userService *UserService) GetTopLevelUserAndAllSubordinates(userId uint) ([]uint, error) {
	// 找到最上级用户
	topLevelUserId, err := userService.GetTopLevelUser(userId)
	if err != nil {
		return nil, err
	}

	// 获取最上级用户下的所有用户（包括最上级用户自己）
	subordinateIds, err := userService.GetSubordinateUserIds(topLevelUserId)
	if err != nil {
		return nil, err
	}

	// 将最上级用户自己也加入列表
	allUserIds := append([]uint{topLevelUserId}, subordinateIds...)
	return allUserIds, nil
}
