package service

import (
	"fmt"
	"os"
	"os/exec"
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

func TestAutoVideoStatusTask_extractAndUploadCover(t *testing.T) {
	// 检查FFmpeg是否可用
	_, err := exec.LookPath("ffmpeg")
	if err != nil {
		t.Skip("FFmpeg未安装，跳过测试")
	}

	// 简单初始化日志，避免空指针错误
	global.GVA_LOG = zap.NewExample()

	// 初始化必要的配置
	global.GVA_CONFIG = config.Server{
		System: config.System{
			OssType: "local",
		},
		Local: config.Local{
			StorePath: "./tmp/upload_test",
			Path:      "/static",
		},
	}

	// 确保目录存在
	err = os.MkdirAll(global.GVA_CONFIG.Local.StorePath, os.ModePerm)
	if err != nil {
		t.Fatalf("创建目录失败: %v", err)
	}
	defer os.RemoveAll("./tmp") // 测试结束后清理

	task := AutoVideoStatusTask{}
	coverUrl, videoDuration, err := task.extractAndUploadCover(
		"http://outin-61a2226203a811f083c300163e18773e.oss-cn-shenzhen.aliyuncs.com/sv/4ac9969-19684ad6b06/4ac9969-19684ad6b06.mp4",
		"test",
	)
	if err != nil {
		t.Fatalf("提取视频首帧失败: %v", err)
	}
	fmt.Printf("封面URL: %s, 视频时长: %d秒\n", coverUrl, videoDuration)
}
