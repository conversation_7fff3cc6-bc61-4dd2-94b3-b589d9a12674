package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ai/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/ims"
	utilsVideo "github.com/flipped-aurora/gin-vue-admin/server/utils/video"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 一键成片任务状态检查
type AutoVideoStatusTask struct {
}

func (s AutoVideoStatusTask) Exec(arg any) (err error) {
	now := time.Now()
	taskTitle := "一键成片任务状态检查"
	global.GVA_LOG.Info(fmt.Sprintf("%s开始执行，时间: %s", taskTitle, now.Format("2006-01-02 15:04:05")))

	// 查询状态为0(处理中)的任务
	var tasks []ai.AutoVideoTask
	err = global.GVA_DB.Where("status = ?", ai.AutoVideoTaskStatusPending).Find(&tasks).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 查询处理中的任务失败", taskTitle), zap.Error(err))
		return err
	}

	if len(tasks) == 0 {
		global.GVA_LOG.Info(fmt.Sprintf("%s: 没有需要处理的任务", taskTitle))
		return nil
	}

	global.GVA_LOG.Info(fmt.Sprintf("%s: 找到 %d 个需要处理的任务", taskTitle, len(tasks)))

	// 创建阿里云客户端
	client, err := ims.NewClient()
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 创建阿里云客户端失败", taskTitle), zap.Error(err))
		return err
	}

	// 将所有状态为0的任务更新为3(处理中)，防止本次定时任务还未处理完，数据又被下次的定时任务处理
	var todoIds []uint
	for _, t := range tasks {
		todoIds = append(todoIds, t.ID)
	}
	global.GVA_DB.Model(&ai.AutoVideoTask{}).Where("id IN (?)", todoIds).Update("status", 3)

	// 定时任务执行完了，但数据还是处于处理中的状态，将状态恢复成未处理状态，以便交给下一次的定时任务执行
	defer func() {
		global.GVA_DB.Model(&ai.AutoVideoTask{}).Where(
			"id IN (?) AND status = ?", todoIds, ai.AutoVideoTaskStatusProcessing,
		).Update("status", ai.AutoVideoTaskStatusPending)
	}()

	// 遍历处理每个任务
	for _, task := range tasks {
		// 调用阿里云API获取任务状态
		batchJob, err := client.GetBatchMediaProducingJob(task.JobId)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("%s: 获取任务 %s 状态失败", taskTitle, task.JobId), zap.Error(err))
			continue
		}

		// 解析任务状态
		status := ai.AutoVideoTaskStatusProcessing // 默认处理中
		if batchJob.Status == "Finished" {
			status = ai.AutoVideoTaskStatusSuccess // 成功
		} else if batchJob.Status == "Fail" {
			status = ai.AutoVideoTaskStatusFailed // 失败
		}

		// 如果任务成功并且有子任务，创建视频记录
		if status == ai.AutoVideoTaskStatusSuccess && len(batchJob.SubJobList) > 0 {
			// 解析分类ID
			var categoryIds []uint
			if task.Categories != "" {
				if err := json.Unmarshal([]byte(task.Categories), &categoryIds); err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("%s: 解析任务 %s 分类信息失败", taskTitle, task.JobId), zap.Error(err))
					categoryIds = []uint{} // 重置为空数组
				}
			}

			// 计算分类分配方式
			categoryCount := len(categoryIds)

			var topicGroups [][]string
			if task.Topics != "" {
				if err := json.Unmarshal([]byte(task.Topics), &topicGroups); err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("%s: 解析任务 %s 话题信息失败", taskTitle, task.JobId), zap.Error(err))
				}
			}

			// 使用ai生成视频标题
			titleNum := len(batchJob.SubJobList)
			var titleList []string
			{
				var scriptReq request.AIScriptRequest
				scriptReq.Model = "doubao-1-5-thinking-pro-250415"
				scriptReq.GenerateCount = titleNum

				// 优先使用视频标题字段
				if task.VideoTitle != "" {
					scriptReq.Prompt = fmt.Sprintf(
						"我是个短视频创作者，我的视频标题是\"%s\"，请帮我改写成更具爆款要素的标题，严格规避抖音违禁敏感词汇，必须严格控制在15个字以内。生成%d个标题，答案不要重复",
						task.VideoTitle,
						titleNum,
					)
					global.GVA_LOG.Info(fmt.Sprintf("%s: 使用视频标题生成AI标题，原标题: %s", taskTitle, task.VideoTitle))
				} else if task.Titles != "" {
					// 其次使用保存的标题生成
					var savedTitles []request.Title
					if err := json.Unmarshal([]byte(task.Titles), &savedTitles); err == nil && len(savedTitles) > 0 {
						// 随机选择一个标题
						randomIndex := rand.Intn(len(savedTitles))
						selectedTitle := savedTitles[randomIndex]
						var titleContent string
						if selectedTitle.Content != "" {
							titleContent = selectedTitle.Content
						} else if len(selectedTitle.Contents) > 0 {
							titleContent = selectedTitle.Contents[0]
						}

						if titleContent != "" {
							scriptReq.Prompt = fmt.Sprintf(
								"我是个短视频创作者，我的视频标题是\"%s\"，请帮我改写成更具爆款要素的标题，严格规避抖音违禁敏感词汇，必须严格控制在15个字以内。生成%d个标题，答案不要重复",
								titleContent,
								titleNum,
							)
							global.GVA_LOG.Info(fmt.Sprintf("%s: 使用保存的标题生成AI标题，原标题: %s", taskTitle, titleContent))
						}
					}
				} else {
					// 如果没有标题，则使用话题生成
					var topicGroups [][]string
					if err := json.Unmarshal([]byte(task.Topics), &topicGroups); err == nil && len(topicGroups) > 0 {
						// 随机选择一个话题组
						randomGroupIndex := rand.Intn(len(topicGroups))
						selectedTopicGroup := topicGroups[randomGroupIndex]
						if len(selectedTopicGroup) > 0 {
							// 从选中的话题组中随机选择一个话题
							randomTopicIndex := rand.Intn(len(selectedTopicGroup))
							selectedTopic := selectedTopicGroup[randomTopicIndex]

							scriptReq.Prompt = fmt.Sprintf(
								"我是个短视频创作者，我要制作关于\"%s\"，请帮我改写成更具爆款要素的标题，严格规避抖音违禁敏感词汇，必须严格控制在15个字以内。生成%d个标题，答案不要重复",
								selectedTopic,
								titleNum,
							)
							global.GVA_LOG.Info(fmt.Sprintf("%s: 使用话题生成AI标题，话题: %s", taskTitle, selectedTopic))
						}
					}
				}

				result, err := service.ServiceGroupApp.AiServiceGroup.AutoVideoService.GenerateAIScript(scriptReq)
				if err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("%s: 生成视频标题失败", taskTitle), zap.Error(err))
					continue
				}
				titleList = result.Scripts
			}

			// 创建视频记录
			for i, subJob := range batchJob.SubJobList {
				var topic string
				if len(topicGroups) > 0 && i < len(topicGroups) {
					topic, _ = jsoniter.MarshalToString(topicGroups[i])
				}

				if subJob.Status != "Success" {
					continue // 只处理成功的子任务
				}

				// 计算当前视频应该分配的分类ID
				var categoryId uint = 0
				if categoryCount > 0 {
					// 如果有选择分类，就按照循环方式分配分类
					categoryId = categoryIds[i%categoryCount]
				}

				// 保存当前子任务信息，避免循环变量引用问题
				currentSubJob := subJob
				currentIndex := i

				// 下载视频并提取首帧作为封面
				coverUrl := ""
				var videoDuration int64

				if currentSubJob.MediaURL != "" {
					// 使用任务ID和索引构建更具唯一性的标识
					subTaskTitle := fmt.Sprintf("%s-子任务%d", taskTitle, currentIndex+1)
					coverUrl, videoDuration, err = s.extractAndUploadCover(currentSubJob.MediaURL, subTaskTitle)
					if err != nil {
						continue
					}

					// 加入短暂延迟，确保资源释放
					time.Sleep(100 * time.Millisecond)
				}

				// 创建视频记录
				var title string
				if len(titleList) > currentIndex {
					title = titleList[currentIndex]
				} else {
					title = fmt.Sprintf("%s-%d", task.TaskName, currentIndex+1)
				}

				video := creative.Video{
					CategoryId:      categoryId,
					Title:           title,
					Cover:           coverUrl,
					Url:             currentSubJob.MediaURL,
					Status:          creative.VideoStatusPending,
					CreatedBy:       task.CreatedBy,
					Source:          creative.VideoSourceAI,
					Topic:           topic,
					TaskId:          task.JobId,
					ManualProductId: task.ManualProductId,
					Duration:        videoDuration, // 设置视频时长
				}

				// 先查询是否存在相同的task_id和url的记录
				var existingVideo creative.Video
				err = global.GVA_DB.Where("task_id = ? AND url = ?", video.TaskId, video.Url).First(&existingVideo).Error

				var result *gorm.DB
				var isUpdate bool
				if errors.Is(err, gorm.ErrRecordNotFound) {
					// 记录不存在，执行插入
					result = global.GVA_DB.Create(&video)
					isUpdate = false
				} else if err != nil {
					// 查询出错
					result = &gorm.DB{Error: err}
					isUpdate = false
				} else {
					// 记录存在，执行更新
					video.ID = existingVideo.ID // 设置ID以便更新
					result = global.GVA_DB.Model(&existingVideo).Updates(map[string]interface{}{
						"category_id":       video.CategoryId,
						"title":             video.Title,
						"cover":             video.Cover,
						"status":            video.Status,
						"created_by":        video.CreatedBy,
						"source":            video.Source,
						"topic":             video.Topic,
						"manual_product_id": video.ManualProductId,
						"updated_at":        time.Now(),
					})
					isUpdate = true
				}

				if result.Error != nil {
					// 更新任务状态为失败
					global.GVA_DB.Model(&ai.AutoVideoTask{}).Where("id = ?", task.ID).Update("status", ai.AutoVideoTaskStatusFailed)
					global.GVA_LOG.Error(fmt.Sprintf("%s: 创建视频记录失败", taskTitle), zap.Error(result.Error))
					continue
				} else {
					// 更新任务状态为成功
					global.GVA_DB.Model(&ai.AutoVideoTask{}).Where("id = ?", task.ID).Update("status", ai.AutoVideoTaskStatusSuccess)
				}

				if result.RowsAffected > 0 {
					if isUpdate {
						global.GVA_LOG.Info(fmt.Sprintf(
							"%s: 为任务 %s 更新视频记录成功，URL: %s",
							taskTitle, task.JobId, currentSubJob.MediaURL,
						))
					} else {
						global.GVA_LOG.Info(fmt.Sprintf(
							"%s: 为任务 %s 创建视频记录成功，URL: %s",
							taskTitle, task.JobId, currentSubJob.MediaURL,
						))
					}
				}
			}
		}
	}

	global.GVA_LOG.Info(fmt.Sprintf("%s执行完毕，时间: %s", taskTitle, time.Now().Format("2006-01-02 15:04:05")))
	return nil
}

// 下载视频、提取首帧并上传为封面，同时获取视频时长
func (s AutoVideoStatusTask) extractAndUploadCover(videoUrl string, taskTitle string) (string, int64, error) {
	// 使用时间戳和URL哈希创建唯一标识符
	uniqueID := fmt.Sprintf("%d-%x", time.Now().UnixNano(), []byte(videoUrl)[:4])

	// 下载视频到临时目录
	resp, err := http.Get(videoUrl)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 下载视频失败，video_url=%s", taskTitle, videoUrl), zap.Error(err))
		return "", 0, err
	}
	defer resp.Body.Close()

	// 创建临时文件（使用唯一ID避免冲突）
	tempFile, err := os.CreateTemp("", fmt.Sprintf("video-%s-*.mp4", uniqueID))
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 创建临时文件失败", taskTitle), zap.Error(err))
		return "", 0, err
	}
	tempPath := tempFile.Name()

	// 确保无论如何都会删除临时文件
	defer func() {
		tempFile.Close()
		os.Remove(tempPath)
	}()

	// 将视频内容写入临时文件
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 写入视频内容失败", taskTitle), zap.Error(err))
		return "", 0, err
	}
	tempFile.Close() // 关闭文件以便后续操作

	// 使用新的 VideoFrameExtractor 提取第一帧并上传到OSS（不压缩），同时获取视频时长
	extractor := utilsVideo.NewVideoFrameExtractor().
		WithVideoPath(tempPath).
		WithCompression(false).
		WithOSSUpload(true).
		WithDuration(true) // 同时获取视频时长

	defer extractor.Cleanup() // 确保清理临时文件

	result, err := extractor.ExtractWithResult()
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 视频帧提取和时长获取失败", taskTitle), zap.Error(err))
		return "", 0, err
	}

	coverUrl := result.ImageURL           // 使用OSS URL作为封面
	videoDuration := result.VideoDuration // 获取视频时长

	global.GVA_LOG.Info(fmt.Sprintf("%s: 成功提取并上传视频首帧，video_url=%s, cover_url=%s, duration=%d", taskTitle, videoUrl, coverUrl, videoDuration))
	return coverUrl, videoDuration, nil
}
