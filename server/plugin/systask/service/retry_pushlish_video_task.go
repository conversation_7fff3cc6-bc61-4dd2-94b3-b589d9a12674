package service

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

// 重发视频任务
type RetryPushlishVideoTask struct {
}

func (s RetryPushlishVideoTask) Exec(arg any) (err error) {
	taskTitle := "重发视频任务"
	// 获取status=1的视频
	var records []creative.AutoPublishVideoRecord
	// 构建查询条件，createdAt在最近7小时内，且状态为4,5,6
	sevenHoursAgo := time.Now().Add(-7 * time.Hour).Format("2006-01-02 15:04:05")
	fmt.Printf("时间点:%s", sevenHoursAgo)
	err = global.GVA_DB.Model(&creative.AutoPublishVideoRecord{}).
		Where("created_at >= ?", sevenHoursAgo).
		Where("status in (?)", []int{4, 5, 6}).Find(&records).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 获取发布列表失败", taskTitle), zap.Error(err))
		return
	}

	rs := []creative.AutoPublishVideoRecord{}
	userMap := make(map[int64]bool)
	for _, record := range records {
		if _, ok := userMap[record.DyUserId]; ok {
			continue
		}
		userMap[record.DyUserId] = true
		rs = append(rs, record)
	}

	s.processRecords(rs)

	global.GVA_LOG.Info(fmt.Sprintf("%s: 执行完毕", taskTitle))
	return
}

func (s RetryPushlishVideoTask) processRecords(records []creative.AutoPublishVideoRecord) {
	taskTitle := "重发视频任务"
	// 定义协程池大小
	const workerCount = 30
	// 创建任务通道
	taskChan := make(chan *creative.AutoPublishVideoRecord)

	dyUserLogic := logic.DyUserLogic{}

	var count int32 // 使用 int32 配合原子操作
	// 启动协程池
	var wg sync.WaitGroup
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for record := range taskChan {
				atomic.AddInt32(&count, 1)

				err := s.processDyUser(record, dyUserLogic)
				if err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("%s:重发失败!", taskTitle), zap.String("nickname", record.Nickname), zap.Error(err))

				} else {
					global.GVA_LOG.Info(fmt.Sprintf("%s:重发结束!", taskTitle), zap.String("nickname", record.Nickname))

				}
			}
		}()
	}

	// 分发任务
	for _, record := range records {
		taskChan <- &record
	}

	// 关闭任务通道
	close(taskChan)

	// 等待所有协程完成
	wg.Wait()

	global.GVA_LOG.Info(fmt.Sprintf("%s: 处理结束，一共处理%d个", taskTitle, atomic.LoadInt32(&count)))
}

/**
 * 处理单个用户
 */
func (s RetryPushlishVideoTask) processDyUser(record *creative.AutoPublishVideoRecord, dyUserLogic logic.DyUserLogic) (err error) {
	dyUserService := service.ServiceGroupApp.DouyinServiceGroup.DyUserService

	fmt.Printf("开始处理用户:%s-%d", record.Nickname, record.DyUserId)
	// 获取用户信息
	dyUser, err := dyUserService.GetUserByID(uint(record.DyUserId))
	if err != nil {
		return err
	}

	// 获取ip状态
	var ip *douyin.IpPool
	err = global.GVA_DB.Model(&douyin.IpPool{}).
		Where("ip =?", dyUser.BindIP).First(&ip).Error
	if err != nil {
		return fmt.Errorf("查询ip信息失败: " + err.Error())
	}
	if ip.HealthStatus != "healthy" {
		return fmt.Errorf("ip状态异常,请重新检查:%s", ip.HealthStatus)
	}

	// 根据req.VideoId获取video表的id
	var video *creative.Video
	err = global.GVA_DB.Model(&creative.Video{}).
		Where("id =?", record.VideoId).First(&video).Error
	if err != nil {
		return err
	}

	// 定义重试信息
	record.RetryCount++
	record.RetryMethod = 2
	record.RetryTime = time.Now().Unix()
	record.Status = 1
	record.Reason = ""

	// 判断cookie是否过期
	loginErr := dyUserLogic.CheckLoginInfo(dyUser)
	if loginErr != nil {
		record.Status = 5
		record.Reason = fmt.Sprintf("%s，请重新登录", loginErr.Error())
		saveLoginErr := global.GVA_DB.Model(&record).Updates(&record).Error
		if saveLoginErr != nil {
			return fmt.Errorf("更新登录状态异常失败:%s", saveLoginErr.Error())
		}

		return fmt.Errorf("登录状态异常:%s", loginErr.Error())
	}

	videoLogic := logic.VideoLogic{}
	err = videoLogic.UploadVideo(record.UniqueId, video, record)

	return
}
