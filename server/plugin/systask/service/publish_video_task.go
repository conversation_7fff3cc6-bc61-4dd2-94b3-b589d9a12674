package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

// 发布抖音视频
type PublishVideoTask struct {
}

func (s PublishVideoTask) Exec(arg any) (err error) {
	taskTitle := "发布抖音视频任务"
	var records []creative.AutoPublishVideoRecord
	err = global.GVA_DB.Where("status = ?", 2).Find(&records).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 获取发布列表失败", taskTitle), zap.Error(err))
		return
	}

	videoService := &service.ServiceGroupApp.CreativeServiceGroup.VideoService
	recordService := &service.ServiceGroupApp.CreativeServiceGroup.AutoPublishVideoRecordService
	for _, record := range records {
		taskWarningFormat := fmt.Sprintf("%s：ID:%d, uniqueId:%s", taskTitle, record.ID, record.UniqueId)

		cookie, err := service.ServiceGroupApp.DouyinServiceGroup.DyUserService.GetAwemeCookie(record.UniqueId)
		if err != nil {
			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			recordService.Fail(&record, fmt.Sprintf("%s:获取cookie失败，id：%d\n", taskWarningFormat, record.VideoId))
			continue
		}

		video, err := service.ServiceGroupApp.CreativeServiceGroup.GetVideo(uint(record.VideoId))
		if err != nil {
			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			recordService.Fail(&record, fmt.Sprintf("%s:查询视频数据GetVideo失败，id：%d\n", taskWarningFormat, record.VideoId))
			continue
		}

		dyUser, err := service.ServiceGroupApp.DouyinServiceGroup.DyUserService.GetUserByID(uint(record.DyUserId))
		if err != nil {
			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			recordService.Fail(&record, fmt.Sprintf("%s:查询抖音用户失败dy_user，id：%d\n", taskWarningFormat, record.DyUserId))
			continue
		}

		publishReq := request.MoreCreatorCustomApiCustomCreateVodRequest{
			Cookie:           cookie,
			Description:      video.Title,
			Proxy:            dyUser.BindIP,
			VisibilityType:   "0",
			UploadPosterPath: video.Cover,
			VideoVid:         record.Vid,
			MusicId:          video.MusicId,
		}
		var topicIds []string
		var topicErrs []string
		if video.Topic != "" {
			var topics []string
			err = json.Unmarshal([]byte(video.Topic), &topics)
			if err != nil {
				_ = videoService.UpdateVideoStatus(record.VideoId, 3)
				recordService.Fail(&record, fmt.Sprintf("%s:解析话题失败，id：%d, topic:%s\n", taskWarningFormat, video.ID, video.Topic))
				continue
			}

			// 用flag标记publishReq.Description是否带"#"，有则flag为true，否则为false
			containTopic := false
			if strings.Contains(publishReq.Description, "#") {
				containTopic = true
			}
			for _, t := range topics {
				tCount := 0
				// 当tCount小于2时，循环获取话题详情
				for tCount < 2 {
					cid, topicErr := s.getTopicDetail(t)
					if topicErr != nil {
						if tCount < 2 {
							tCount++
							continue
						} else {
							topicErrs = append(topicErrs, fmt.Sprintf("请求话题详情失败：topic:%s, err：%+v\n", t, err))
							break
						}
					}
					if cid != "" {
						topicIds = append(topicIds, cid)
						if !containTopic {
							publishReq.Description = fmt.Sprintf("%s #%s", publishReq.Description, t)
						}
						break
					}
				}
			}

			if len(topicErrs) > 0 {
				_ = videoService.UpdateVideoStatus(record.VideoId, 3)
				recordService.Fail(&record, fmt.Sprintf("%s:解析话题失败，id：%d, err：%+v\n", taskWarningFormat, video.ID, topicErrs))
				continue
			}

			if len(topicIds) > 0 {
				challengeBytes, _ := json.Marshal(topicIds)
				publishReq.Challenges = string(challengeBytes)
			}
		}

		if video.ProductId != "" {
			publishReq.ProductUrl = fmt.Sprintf("%s%s", global.GVA_CONFIG.Douyin.ProductUrl, video.ProductId)
		}

		// 如果视频带推广链接则使用Mix的发布接口
		var createResp response.MoreCreatorCustomApiCustomCreateVodResponse
		var createErr error

		// 获取商品信息
		var productInfo *douyin.DyProductManual
		if video.ManualProductId > 0 {
			err := global.GVA_DB.First(&productInfo, video.ManualProductId).Error
			if err != nil {
				global.GVA_LOG.Error(fmt.Sprintf("%s: 获取商品信息失败", taskTitle), zap.Error(err))
			}
		}

		// 判断是否有带货信息
		hasPromotion := productInfo != nil && productInfo.PromotionUrl != ""

		if !hasPromotion {
			createResp, createErr = service.ServiceGroupApp.DouyinServiceGroup.MoreCreatorCustomApiService.CustomCreateVod(publishReq)
			if createErr != nil {
				_ = videoService.UpdateVideoStatus(record.VideoId, 3)
				recordService.Fail(&record, fmt.Sprintf("%s:请求发布视频失败，record_id：%d，request：%+v, createErr%+v\n", taskWarningFormat, record.ID, publishReq, createErr))
				continue
			}
		} else {
			fmt.Printf("发布带货视频:%s", dyUser.UniqueId)
			// 从redis获取webCookie
			redisKey := fmt.Sprintf(douyin.DyWebCookieKey, dyUser.UniqueId)
			webCookie, WebCookieErr := global.GVA_REDIS.Get(context.Background(), redisKey).Result()
			if WebCookieErr != nil || webCookie == "" {
				_ = videoService.UpdateVideoStatus(record.VideoId, 3)
				recordService.Fail(&record, fmt.Sprintf("%s:请求发布带货视频失败，record_id：%d，请先进行营销授权：%v\n", taskWarningFormat, record.ID, WebCookieErr))
				continue
			}

			promotionPublishReq := request.MoreCreatorMixApiCreatePromotionRequest{
				Cookie:           cookie,
				WebCookie:        webCookie,
				Proxy:            dyUser.BindIP,
				PromotionLink:    productInfo.PromotionUrl,
				PromotionTitle:   productInfo.PromotionTitle,
				Description:      video.Title,
				VisibilityType:   "0",
				UploadPosterPath: video.Cover,
				VideoVid:         record.Vid,
			}
			createResp, createErr = service.ServiceGroupApp.DouyinServiceGroup.MoreCreatorMixApiService.PublishPromotionVod(promotionPublishReq)
			if createErr != nil {
				_ = videoService.UpdateVideoStatus(record.VideoId, 3)
				recordService.Fail(&record, fmt.Sprintf("%s:请求发布带货视频失败，record_id：%d，createErr%+v\n", taskWarningFormat, record.ID, createErr))
				continue
			}
		}

		if createResp.Data.EncryptUid != "" {
			record.Status = 7
			record.EncryptUid = createResp.Data.EncryptUid
			recordService.Fail(&record, fmt.Sprintf("%s:发布视频失败，record_id：%d，需要短信验证！resp:%+v", taskWarningFormat, record.ID, createResp))
			continue
		}

		if createResp.Data.StatusCode != 0 {
			_ = videoService.UpdateVideoStatus(record.VideoId, 3)
			recordService.Fail(&record, fmt.Sprintf("%s:发布视频失败，record_id：%d，request：%+v, status_code:%d, status_msg: %s", taskWarningFormat, record.ID, publishReq, createResp.Data.StatusCode, createResp.Data.StatusMsg))
			continue
		}

		videoUpdateData := map[string]interface{}{
			"status":   1,
			"aweme_id": createResp.Data.ItemId,
		}
		if err := global.GVA_DB.Model(&video).Where("id = ?", record.VideoId).Updates(videoUpdateData).Error; err != nil {
			recordService.Fail(&record, fmt.Sprintf("%s:保存视频状态失败, record_id：%d，createErr%+v\n", taskWarningFormat, record.ID, err))
			continue
		}

		record.AwemeId = createResp.Data.ItemId
		recordService.Success(&record)
	}

	return nil
}

// 获取话题详情
func (s PublishVideoTask) getTopicDetail(keyword string) (cid string, err error) {
	cdResp, err := service.ServiceGroupApp.DouyinServiceGroup.MoreApiService.CahllengeDetail(keyword)
	if err != nil {
		return "", fmt.Errorf("请求话题详情失败：topic:%s, err：%+v\n", keyword, err)
	}
	if cdResp.Data.ChInfo.Cid == "" {
		return "", fmt.Errorf("获取话题详情失败：topic:%s, status_code: %d, status_msg:%s\n", keyword, cdResp.Data.StatusCode, cdResp.Data.StatusMsg)
	}

	return cdResp.Data.ChInfo.Cid, nil
}
