package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

type GetAutoPublishVideoLogSearch struct {
	request.PageInfo
	SysUserIds []uint `json:"sysUserIds" form:"sysUserIds"` // 系统用户id
	UniqueId   string `json:"uniqueId" form:"uniqueId"`
	Status     int    `json:"status" form:"status"`
}

type RePublishRequest struct {
	RecordId uint `json:"recordId" binding:"required"`
}
