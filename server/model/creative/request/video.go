package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// VideoSearch 视频搜索
type VideoSearch struct {
	Title        string `json:"title" form:"title"`               // 标题
	CategoryId   *uint  `json:"categoryId" form:"categoryId"`     // 分类ID
	Status       *int   `json:"status" form:"status"`             // 状态
	HasPromotion *bool  `json:"hasPromotion" form:"hasPromotion"` // 是否有带货信息
	request.PageInfo
}

// VideoCreate 创建视频
type VideoCreate struct {
	ID              uint   `json:"id" form:"id"`                                    // ID
	CategoryId      uint   `json:"categoryId" form:"categoryId" binding:"required"` // 分类ID
	Title           string `json:"title" form:"title" binding:"required"`           // 标题
	Topic           string `json:"topic" form:"topic"`                              // 话题
	Status          int    `json:"status" form:"status"`                            // 状态
	ManualProductId uint   `json:"manualProductId" form:"manualProductId"`          // 手动录入商品ID
	Duration        int64  `json:"duration" form:"duration"`                        // 视频时长（秒）
}

// VideoDelete 删除视频
type VideoDelete struct {
	ID uint `json:"id" form:"id"` // ID
}

// VideoSetCover 设置封面
type VideoSetCover struct {
	ID    uint   `json:"id" form:"id"`       // ID
	Cover string `json:"cover" form:"cover"` // 封面链接
}
