package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
)

// VideoResponse 视频响应
type VideoResponse struct {
	ID              uint                    `json:"id"`                     // ID
	CategoryId      uint                    `json:"categoryId"`             // 分类ID
	CategoryName    string                  `json:"categoryName"`           // 分类名称
	Title           string                  `json:"title"`                  // 标题
	Cover           string                  `json:"cover"`                  // 封面链接
	Url             string                  `json:"url"`                    // 视频链接
	Status          int                     `json:"status"`                 // 状态
	CreatedAt       time.Time               `json:"createdAt" form:"name:"` // 创建时间
	Source          int                     `json:"source"`                 // 来源
	Topic           string                  `json:"topic"`                  // 话题
	ManualProductId uint                    `json:"manualProductId"`        // 手动录入商品ID
	ManualProduct   *douyin.DyProductManual `json:"manualProduct"`          // 关联的商品信息
	Duration        int64                   `json:"duration"`               // 视频时长（秒）
}

// VideoList 视频列表
type VideoList struct {
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
}
