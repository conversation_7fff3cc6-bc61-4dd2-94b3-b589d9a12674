package creative

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Video 视频
type Video struct {
	global.GVA_MODEL
	CategoryId      uint   `json:"categoryId" gorm:"comment:分类ID"`
	Title           string `json:"title" gorm:"comment:标题"`
	Cover           string `json:"cover" gorm:"comment:封面链接"`
	Url             string `json:"url" gorm:"comment:视频链接"`
	ProductId       string `json:"productId" gorm:"comment:产品ID"`
	Status          int    `json:"status" gorm:"comment:状态:0-待发布 1-已发布 2-生成中 3-发布失败 4-发布中"`
	CreatedBy       uint   `json:"createdBy" gorm:"comment:创建人"`
	Source          int    `json:"source" gorm:"comment:来源:0-手动增加 1-智能生成"`
	PostId          uint   `json:"postId" gorm:"comment:对标作品ID"`
	MusicId         string `json:"musicId" gorm:"comment:音乐ID"`
	Topic           string `json:"topic" gorm:"comment:话题"`
	TaskId          string `json:"taskId" gorm:"comment:任务ID"`
	ManualProductId uint   `json:"manualProductId" gorm:"comment:手动录入商品ID"`
	AwemeId         string `json:"awemeId" gorm:"comment:抖音作品ID"`
	Duration        int64  `json:"duration" gorm:"comment:视频时长"`
}

func (Video) TableName() string {
	return "video"
}
