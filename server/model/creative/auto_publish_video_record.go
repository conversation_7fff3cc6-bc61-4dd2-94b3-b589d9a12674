package creative

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// AutoPublishVideo 自动发布视频记录
type AutoPublishVideoRecord struct {
	global.GVA_MODEL
	DyUserId        int64  `gorm:"type:bigint;default:0;comment:抖音用户id;index" json:"dyUserId"`
	Weekday         int    `gorm:"type:tinyint;default:0;comment:星期天数;index" json:"weekday"`
	Hour            int    `gorm:"type:tinyint;default:0;comment:小时数" json:"hour"`
	Minute          int    `gorm:"type:tinyint;default:0;comment:分钟数" json:"minute"`
	Type            int    `gorm:"type:tinyint;default:0;comment:发布类型：1-视频；2-图文" json:"type"`
	VideoCategoryId int    `gorm:"type:int;default:0;comment:视频库分类id;index" json:"videoCategoryId"`
	SysUserId       int64  `gorm:"type:bigint;default:0;comment:管理员id;index" json:"sysUserId"`
	Status          int    `gorm:"type:tinyint;default:0;index;comment:状态：1-视频上传中 2-视频上传完成 3-视频发布成功 4-视频发布失败 5-视频上传失败 6-查询视频上传结果失败 7-需要短信验证" json:"status"`
	Reason          string `gorm:"type:varchar(2048);default:'';comment:失败原因" json:"reason"`
	Nickname        string `gorm:"type:varchar(255);default:'';comment:抖音昵称" json:"nickname"`
	UniqueId        string `gorm:"type:varchar(255);default:'';comment:抖音号;index" json:"uniqueId"`
	TaskId          string `gorm:"type:varchar(255);default:'';comment:发布视频任务id" json:"taskId"`
	Vid             string `gorm:"type:varchar(255);default:'';comment:抖音下发视频id" json:"vid"`
	VideoId         uint   `gorm:"type:bigint;default:0;comment:video表的主键id;index" json:"videoId"`
	Title           string `gorm:"type:varchar(255);default:'';comment:作品标题" json:"title"`
	EncryptUid      string `gorm:"type:varchar(255);default:'';comment:加密用户id" json:"encryptUid"`
	AwemeId         string `gorm:"type:varchar(255);default:'';comment:抖音视频id" json:"awemeId"`
	CommenTraceCid  string `json:"commenTraceCid" gorm:"column:comment_trace_cid;comment:评论追踪话题ID"`
	TimingType      int    `gorm:"type:tinyint;default:0;comment:定时类型：0-定时触发，1-手动发布" json:"timingType"`
	RetryCount      int    `gorm:"type:int;default:0;comment:重试次数" json:"retryCount"`
	RetryTime       int64  `gorm:"type:int;default:0;comment:重试时间" json:"retryTime"`
	RetryMethod     int    `gorm:"type:int;default:0;comment:重试方式：1-手动，2-自动" json:"retryMethod"`
}

func (AutoPublishVideoRecord) TableName() string {
	return "auto_publish_video_record"
}
