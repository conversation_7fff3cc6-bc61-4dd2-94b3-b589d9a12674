package douyin

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

type DyUser struct {
	global.GVA_MODEL
	ImToken    string `json:"imToken" gorm:"column:im_token;comment:火苗token"`
	CategoryId uint   `json:"categoryId" gorm:"column:category_id;comment:分类ID"`
	// 从 HuomiaoService.GetUserInfo 返回的字段
	UID                string `json:"uid" gorm:"index;comment:用户ID"`
	Nickname           string `json:"nickname" gorm:"comment:用户昵称"`
	Avatar             string `json:"avatar" gorm:"type:varchar(2000);comment:头像"`
	UniqueId           string `json:"uniqueId" gorm:"comment:抖音号"`
	ShortId            string `json:"shortId" gorm:"comment:短ID"`
	SecUid             string `json:"secUid" gorm:"index;comment:用户sec_uid"`
	FollowerCount      int64  `json:"followerCount" gorm:"comment:粉丝数"`
	FollowingCount     int64  `json:"followingCount" gorm:"comment:关注数"`
	AwemeCount         int64  `json:"awemeCount" gorm:"comment:作品数"`
	TotalFavorited     int64  `json:"totalFavorited" gorm:"comment:获赞数"`
	AccountRegion      string `json:"accountRegion" gorm:"comment:账号地区"`
	Province           string `json:"province" gorm:"comment:省份"`
	City               string `json:"city" gorm:"comment:城市"`
	CollegeName        string `json:"collegeName" gorm:"comment:学校"`
	BindPhone          string `json:"bindPhone" gorm:"comment:绑定手机号"`
	Birthday           string `json:"birthday" gorm:"comment:生日"`
	Gender             int    `json:"gender" gorm:"comment:性别"`
	Signature          string `json:"signature" gorm:"comment:个性签名"`
	SysUserId          int64  `json:"sysUserId" gorm:"index;comment:系统用户ID"`
	SysUserName        string `json:"sysUserName" gorm:"-"`   // 系统用户昵称（关联字段）
	PhoneUserName      string `json:"phoneUserName" gorm:"-"` // 手机号实名（关联字段）
	PhoneOperator      string `json:"phoneOperator" gorm:"-"` // 手机号运营商（关联字段）
	IsProductEnabled   bool   `json:"isProductEnabled" gorm:"column:is_product_enabled;default:0;comment:是否开启选品 0-关闭 1-开启"`
	BindIP             string `json:"bindIP" gorm:"column:bind_ip;comment:绑定IP地址"`
	BindDevice         string `json:"bindDevice" gorm:"column:bind_device;comment:绑定设备"`
	Did                string `json:"did" gorm:"column:did;comment:设备ID"`
	Iid                string `json:"iid" gorm:"column:iid;comment:用户身份ID"`
	Status             int    `json:"status" gorm:"column:status;default:0;comment:用户状态：0-未授权 1-正常 2-登录失效 3-登录超时"`
	WithdrawableAmount int    `json:"withdrawableAmount" gorm:"column:withdrawable_amount;default:0;comment:可提现金额"`
	Day1TotalAmount    int    `json:"day1TotalAmount" gorm:"column:day1_total_amount;default:0;comment:昨日总金额"`
	Day7TotalAmount    int    `json:"day7TotalAmount" gorm:"column:day7_total_amount;default:0;comment:七日总金额"`
	TotalAmount        int    `json:"totalAmount" gorm:"column:total_amount;default:0;comment:总金额"`
	RealName           string `json:"realName" gorm:"column:real_name;comment:真实姓名"`
	AccountType        int    `json:"accountType" gorm:"column:account_type;default:1;comment:账号类型：1-个人号 2-企业号号"`
	AutoPublishStatus  int    `json:"autoPublishStatus" gorm:"column:auto_publish_status;default:0;comment:自动发布状态：0-未设置 1-开启 2-暂停"`
	TalkAuthStatus     int    `json:"talkAuthStatus" gorm:"column:talk_auth_status;default:0;type:tinyint;comment:私信授权状态：0-未授权 1-已授权 2-授权失效 3-禁用"`
	BiteBrowserId      string `json:"biteBrowserId" gorm:"column:bite_browser_id;type:varchar(64);comment:比特浏览器ID"`
	Remark             string `json:"remark" gorm:"column:remark;comment:备注"`
	CommentTraceStatus int    `json:"commentTraceStatus" gorm:"column:comment_trace_status;default:0;comment:评论追踪状态：0-未开启 1-开启"`
	CommentTemplates   string `json:"commentTemplates" gorm:"column:comment_templates;type:varchar(2000);comment:评论模板"`
	CommentKeyword     string `json:"commentKeyword" gorm:"column:comment_keyword;type:varchar(512);comment:评论关键词"`
	ReplyTemplates     string `json:"replyTemplates" gorm:"column:reply_templates;type:varchar(2000);comment:回复模板"`
	PromotionLink      string `json:"promotionLink" gorm:"column:promotion_link;type:varchar(512);comment:推广链接"`
	LoginRetry         int    `json:"loginRetry" gorm:"column:login_retry;default:0;comment:登录重试次数"`
	VideoCategoryId    int    `json:"videoCategoryId" gorm:"column:video_category_id;default:0;comment:视频分类ID"`
}

func (DyUser) TableName() string {
	return "dy_user"
}
