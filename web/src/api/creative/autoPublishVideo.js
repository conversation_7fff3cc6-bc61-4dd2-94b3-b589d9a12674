import service from '@/utils/request';

// 根据抖音用户id获取自动发布记录列表
export const getAutoPublishVideoListByDyUserId = (params) => {
    return service({
        url: '/auto-publish-video/list',
        method: 'get',
        params
    });
};

// 保存自动发布记录
export const saveAutoPublishVideo = (data) => {
    return service({
        url: '/auto-publish-video/save',
        method: 'post',
        data
    });
};

// 修改自动发布记录状态
export const changeAutoPublishVideoStauts = (data) => {
    return service({
        url: '/auto-publish-video/change-status',
        method: 'post',
        data
    });
};

export const getAutoPublishVideoLogList = (params) => {
    return service({
        url: '/auto-publish-video/log-list',
        method: 'get',
        params
    });
};

// 重新发布
export const republishVideo = (data) => {
    return service({
        url: '/auto-publish-video/re-publish',
        method: 'post',
        data
    });
};