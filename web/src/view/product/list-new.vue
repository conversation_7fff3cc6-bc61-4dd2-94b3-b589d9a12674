<template>
  <div class="product-list-container">
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="抖音用户">
          <el-select
            v-model="searchInfo.dyUserIds"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择抖音用户"
            style="width: 350px"
          >
            <el-option-group v-for="group in dyUserGroups" :key="group.sysNickname" :label="group.sysNickname">
              <el-option v-for="user in group.dyUsers" :key="user.id" :label="user.nickname" :value="user.id" />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="商品标题">
          <el-input v-model="searchInfo.title" placeholder="请输入商品标题" style="width: 200px" />
        </el-form-item>
        <el-form-item label="录入时间">
          <el-date-picker
            v-model="searchInfo.selectTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="w-80"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getTableData">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleAdd">录入商品</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading">
      <!-- 商品基本信息 -->
      <el-table-column label="商品信息" min-width="200">
        <template #default="scope">
          <div class="product-info">
            <div class="product-detail">
              <div class="product-title">{{ scope.row.title }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="商品带货标题" min-width="200">
        <template #default="scope">
          <div>{{ scope.row.promotionTitle || '-' }}</div>
        </template>
      </el-table-column>

      <el-table-column label="商品带货链接" min-width="140">
        <template #default="scope">
          <div class="promotion-links-container">
            <!-- 带货链接1 -->
            <div class="promotion-link-row" v-if="scope.row.promotionUrl">
              <a :href="scope.row.promotionUrl" target="_blank" class="promotion-link">
                <el-icon class="cart-icon"><ShoppingCart /></el-icon>
                <span class="link-text">购物车链接</span>
                <el-tag v-if="scope.row.isAlliance" type="success" size="small" class="alliance-tag">团</el-tag>
              </a>
              <el-button type="primary" link @click="copyUrl(scope.row.promotionUrl)" class="copy-btn">
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
            <!-- 带货链接2 -->
            <div class="promotion-link-row" v-if="scope.row.promotionUrl2">
              <a :href="scope.row.promotionUrl2" target="_blank" class="promotion-link">
                <el-icon class="cart-icon"><ShoppingCart /></el-icon>
                <span class="link-text">购物车链接2</span>
                <el-tag v-if="scope.row.isAlliance2" type="success" size="small" class="alliance-tag">团</el-tag>
              </a>
              <el-button type="primary" link @click="copyUrl(scope.row.promotionUrl2)" class="copy-btn">
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="佣金" align="center" width="150">
        <template #default="scope">
          <div class="commission-rate">{{ scope.row.cosRatio.toFixed(2) }}%</div>
        </template>
      </el-table-column>

      <el-table-column label="录入时间" align="center" width="150">
        <template #default="scope">
          <div>{{ formatDateTime(scope.row.CreatedAt) }}</div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="gva-pagination">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 录入/编辑商品对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '录入商品' : '编辑商品'"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="formData" label-width="100px">
        <el-form-item label="商品标题" required>
          <el-input v-model="formData.title" placeholder="请输入商品标题" />
        </el-form-item>
        <el-form-item label="带货标题">
          <el-input v-model="formData.promotionTitle" placeholder="请输入带货标题" />
        </el-form-item>
        <el-form-item label="带货链接">
          <div style="display: flex; align-items: center; width: 100%">
            <el-input v-model="formData.promotionUrl" placeholder="请输入带货链接" style="flex-grow: 1" />
            <el-checkbox v-model="formData.isAlliance" style="margin-left: 10px; flex-shrink: 0">团长链接</el-checkbox>
          </div>
        </el-form-item>
        <el-form-item label="带货链接2">
          <div style="display: flex; align-items: center; width: 100%">
            <el-input v-model="formData.promotionUrl2" placeholder="请输入带货链接2" style="flex-grow: 1" />
            <el-checkbox v-model="formData.isAlliance2" style="margin-left: 10px; flex-shrink: 0">团长链接</el-checkbox>
          </div>
        </el-form-item>
        <el-form-item label="佣金比例">
          <el-input-number
            v-model="formData.cosRatio"
            :min="0"
            :max="100"
            :precision="2"
            :step="0.01"
            :formatter="(value) => `${value}%`"
            :parser="(value) => value.replace('%', '')"
          />
          <span style="margin-left: 10px">%</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import {
    createProductManual,
    updateProductManual,
    deleteProductManual,
    getProductManualList,
    getProductUserList
  } from '@/api/douyin/product'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { DocumentCopy, ShoppingCart } from '@element-plus/icons-vue'

  const loading = ref(false)
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const tableData = ref([])
  const dyUserGroups = ref([])
  const searchInfo = ref({
    dyUserIds: [],
    title: '',
    selectTimeRange: []
  })

  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // 'add' 或 'edit'
  const formData = ref({
    title: '',
    promotionTitle: '',
    promotionUrl: '',
    promotionUrl2: '',
    cosRatio: 0,
    isAlliance: false,
    isAlliance2: false
  })

  // 获取抖音用户列表
  const getDyUserList = async () => {
    try {
      const res = await getProductUserList()
      if (res.code === 0) {
        dyUserGroups.value = res.data.list
      }
    } catch (err) {
      console.error('获取抖音用户列表失败:', err)
      ElMessage.error('获取抖音用户列表失败')
    }
  }

  // 获取列表数据
  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        page: page.value,
        pageSize: pageSize.value,
        title: searchInfo.value.title
      }

      // 如果选择了抖音用户，则添加到查询参数中
      if (searchInfo.value.dyUserIds && searchInfo.value.dyUserIds.length > 0) {
        params.dyUserId = searchInfo.value.dyUserIds[0]
      }

      // 添加录入时间范围
      if (searchInfo.value.selectTimeRange && searchInfo.value.selectTimeRange.length === 2) {
        params.startTime = searchInfo.value.selectTimeRange[0]
        params.endTime = searchInfo.value.selectTimeRange[1]
      }

      const res = await getProductManualList(params)
      if (res.code === 0) {
        tableData.value = res.data.list
        total.value = res.data.total
      }
    } catch (err) {
      console.error('获取商品列表失败:', err)
      ElMessage.error('获取商品列表失败')
    } finally {
      loading.value = false
    }
  }

  // 格式化日期时间
  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return ''
    try {
      const date = new Date(dateTimeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
        2,
        '0'
      )} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    } catch (e) {
      console.error('格式化日期时间失败:', e)
      return dateTimeStr
    }
  }

  // 重置搜索条件
  const resetSearch = () => {
    searchInfo.value = {
      dyUserIds: [],
      title: '',
      selectTimeRange: []
    }
    page.value = 1
    pageSize.value = 10
  }

  // 复制链接
  const copyUrl = async (url) => {
    if (!url) {
      ElMessage.warning('链接不存在')
      return
    }

    try {
      await navigator.clipboard.writeText(url)
      ElMessage.success('链接已复制到剪贴板')
    } catch {
      // 降级处理：使用旧的方法
      const textArea = document.createElement('textarea')
      textArea.value = url
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        ElMessage.success('链接已复制到剪贴板')
      } catch (fallbackErr) {
        ElMessage.error('复制失败，请手动复制')
        console.error('复制失败:', fallbackErr)
      }
      document.body.removeChild(textArea)
    }
  }

  // 分页相关方法
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 录入商品
  const handleAdd = () => {
    dialogType.value = 'add'
    formData.value = {
      title: '',
      promotionTitle: '',
      promotionUrl: 'https://haohuo.jinritemai.com/views/product/item2?id=',
      promotionUrl2: 'https://haohuo.jinritemai.com/views/product/item2?id=',
      cosRatio: 0,
      isAlliance: false,
      isAlliance2: false
    }
    dialogVisible.value = true
  }

  // 编辑商品
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    formData.value = {
      id: row.ID,
      title: row.title,
      promotionTitle: row.promotionTitle,
      promotionUrl: row.promotionUrl,
      promotionUrl2: row.promotionUrl2 || '',
      cosRatio: Number(row.cosRatio) || 0,
      isAlliance: row.isAlliance,
      isAlliance2: row.isAlliance2
    }
    dialogVisible.value = true
  }

  // 删除商品
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(`确定要删除商品 "${row.title}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteProductManual(row.ID)
      ElMessage.success('删除成功')
      getTableData()
    } catch (err) {
      if (err !== 'cancel') {
        console.error('删除失败:', err)
        ElMessage.error('删除失败')
      }
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      const data = {
        ...formData.value,
        cosRatio: Number(formData.value.cosRatio) || 0
      }

      // 如果选择了抖音用户，则添加到提交数据中
      if (searchInfo.value.dyUserIds && searchInfo.value.dyUserIds.length > 0) {
        data.dyUserId = searchInfo.value.dyUserIds[0]
      }

      if (dialogType.value === 'add') {
        await createProductManual(data)
        ElMessage.success('录入成功')
      } else {
        await updateProductManual(data.id, data)
        ElMessage.success('编辑成功')
      }

      dialogVisible.value = false
      getTableData()
    } catch (err) {
      console.error(dialogType.value === 'add' ? '录入失败:' : '编辑失败:', err)
      ElMessage.error(dialogType.value === 'add' ? '录入失败' : '编辑失败')
    }
  }

  onMounted(() => {
    getDyUserList()
    getTableData()
  })
</script>

<style lang="scss" scoped>
  .product-list-container {
    padding: 18px;

    .gva-search-box {
      margin-bottom: 16px;
    }

    .gva-pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }

    .product-info {
      display: flex;
      align-items: center;

      .product-detail {
        display: flex;
        flex-direction: column;

        .product-title {
          font-size: 14px;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .product-shop {
          color: #909399;
          font-size: 12px;
        }
      }
    }

    .commission-rate {
      color: #909399;
      font-size: 12px;
    }

    .avatar-uploader,
    .avatar-uploader-icon,
    .avatar {
      display: none;
    }

    .promotion-links-container {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .promotion-link-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
      transition: background-color 0.2s;
    }

    .promotion-link-row:hover {
      background-color: #e9ecef;
    }

    .promotion-link {
      color: #409eff;
      font-size: 13px;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      flex: 1;
    }

    .promotion-link:hover {
      color: #1890ff;
    }

    .cart-icon {
      font-size: 16px;
      color: #409eff;
      flex-shrink: 0;
    }

    .link-text {
      font-weight: 500;
    }

    .alliance-tag {
      margin-left: 4px;
    }

    .copy-btn {
      padding: 6px !important;
      margin-left: 8px;
      background-color: rgba(64, 158, 255, 0.1);
      border-radius: 4px;
      transition: all 0.2s;
      min-width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .copy-btn:hover {
      background-color: rgba(64, 158, 255, 0.2);
      transform: scale(1.05);
    }

    .copy-btn .el-icon {
      margin: 0;
    }
  }
</style>
