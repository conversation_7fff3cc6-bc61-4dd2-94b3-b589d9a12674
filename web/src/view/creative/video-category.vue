<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="分类名称">
          <el-input v-model="searchInfo.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="primary" @click="openDialog(null)">新增一级分类</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
      row-key="ID"
      stripe
      border
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="name" label="分类名称" width="220" />
      <el-table-column prop="sort" label="排序值" width="100" />
      <el-table-column prop="pendingVideoCount" label="待发布" width="120">
        <template #default="scope">
          <div>
            <el-button type="primary" link @click="viewPendingVideos(scope.row)">
              {{ scope.row.pendingVideoCount }}
            </el-button>
            <span v-if="scope.row.children && scope.row.children.length > 0" style="color: #909399; margin-left: 5px">
              ({{ getTotalPendingCount(scope.row) }})
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="videoCount" label="视频数" width="120">
        <template #default="scope">
          <div>
            <el-button type="primary" link @click="viewVideos(scope.row)">
              {{ scope.row.videoCount }}
            </el-button>
            <span v-if="scope.row.children && scope.row.children.length > 0" style="color: #909399; margin-left: 5px">
              ({{ getTotalVideoCount(scope.row) }})
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="creatorName" label="创建人" width="180" />
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="2"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="CreatedAt" label="创建时间">
        <template #default="scope">
          {{ formatDate(scope.row.CreatedAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button type="primary" link @click="editCategory(scope.row)">编辑</el-button>
          <el-button type="primary" link @click="deleteCategory(scope.row)">删除</el-button>
          <el-button v-if="!scope.row.parentId" type="primary" link @click="openDialog(scope.row.ID)"
            >添加子分类</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="page"
      :page-size="pageSize"
      :page-sizes="[10, 30, 50, 100]"
      :small="small"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog v-model="dialogFormVisible" :title="type === 'add' ? '新增视频分类' : '编辑视频分类'">
      <el-form :model="form" label-width="100px">
        <el-form-item label="父级分类" v-if="form.parentId">
          <el-input v-model="parentCategoryName" disabled />
        </el-form-item>
        <el-form-item label="分类名称">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="form.status" :active-value="1" :inactive-value="2" />
        </el-form-item>
        <el-form-item label="排序值">
          <el-input-number v-model="form.sort" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="enterDialog">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { formatDate } from '@/utils/format'
  import { useRouter } from 'vue-router'
  import {
    createVideoCategory,
    deleteVideoCategory,
    updateVideoCategory,
    getVideoCategoryList,
    setVideoCategoryStatus
  } from '@/api/creative/videoCategory'

  const router = useRouter()
  const page = ref(1)
  const total = ref(0)
  const pageSize = ref(10)
  const tableData = ref([])
  const searchInfo = ref({})
  const form = ref({
    name: '',
    sort: 0,
    parentId: null,
    status: 1
  })
  const parentCategoryName = ref('')

  const type = ref('add')
  const dialogFormVisible = ref(false)
  const loading = ref(false)

  // 查询
  const getTableData = async () => {
    loading.value = true
    try {
      const table = await getVideoCategoryList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
      if (table.code === 0) {
        tableData.value = table.data.list
        total.value = table.data.total
        page.value = table.data.page
        pageSize.value = table.data.pageSize
      }
    } catch (err) {
      console.log(err)
      ElMessage({
        type: 'error',
        message: '获取列表失败'
      })
    } finally {
      loading.value = false
    }
  }

  // 查询
  const onSubmit = () => {
    page.value = 1
    pageSize.value = 10
    getTableData()
  }

  // 递归查找父分类
  const findParent = (categories, parentId) => {
    for (const category of categories) {
      if (category.ID === parentId) {
        return category
      }
      if (category.children && category.children.length > 0) {
        const found = findParent(category.children, parentId)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  // 打开弹窗
  const openDialog = (parentId = null) => {
    type.value = 'add'
    if (parentId) {
      form.value.parentId = parentId
      const parent = findParent(tableData.value, parentId)
      if (parent) {
        parentCategoryName.value = parent.name
      }
    } else {
      form.value.parentId = null
      parentCategoryName.value = ''
    }
    dialogFormVisible.value = true
  }

  // 关闭弹窗
  const closeDialog = () => {
    dialogFormVisible.value = false
    form.value = {
      name: '',
      sort: 0,
      parentId: null,
      status: 1
    }
    parentCategoryName.value = ''
  }

  // 确定弹窗
  const enterDialog = async () => {
    let res
    switch (type.value) {
      case 'add':
        res = await createVideoCategory(form.value)
        break
      case 'edit':
        res = await updateVideoCategory(form.value)
        break
      default:
        res = await createVideoCategory(form.value)
        break
    }

    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: type.value === 'add' ? '添加成功' : '编辑成功'
      })
      closeDialog()
      getTableData()
    }
  }

  // 编辑
  const editCategory = (row) => {
    type.value = 'edit'
    form.value = { ...row }
    if (form.value.parentId) {
      const parent = findParent(tableData.value, form.value.parentId)
      if (parent) {
        parentCategoryName.value = parent.name
      }
    } else {
      parentCategoryName.value = ''
    }
    dialogFormVisible.value = true
  }

  // 删除
  const deleteCategory = async (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await deleteVideoCategory({ ID: row.ID })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === 1 && page.value > 1) {
          page.value--
        }
        getTableData()
      }
    })
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 查看分类下的视频
  const viewVideos = (row) => {
    router.push({
      name: 'creativeVideo',
      query: {
        categoryId: row.ID,
        viewMode: 'table'
      }
    })
  }

  // 查看分类下的待发布视频
  const viewPendingVideos = (row) => {
    router.push({
      name: 'creativeVideo',
      query: { categoryId: row.ID, status: 0, viewMode: 'table' }
    })
  }

  const handleStatusChange = async (row) => {
    const res = await setVideoCategoryStatus({ id: row.ID, status: row.status })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '状态设置成功'
      })
    } else {
      // 状态反转以匹配UI
      row.status = row.status === 1 ? 2 : 1
      ElMessage({
        type: 'error',
        message: '状态设置失败'
      })
    }
  }

  // 递归计算待发布视频总数（包含子分类）
  const getTotalPendingCount = (row) => {
    if (!row.children || row.children.length === 0) {
      return row.pendingVideoCount || 0
    }
    let totalCount = row.pendingVideoCount || 0
    for (const child of row.children) {
      totalCount += getTotalPendingCount(child)
    }
    return totalCount
  }

  // 递归计算视频总数（包含子分类）
  const getTotalVideoCount = (row) => {
    if (!row.children || row.children.length === 0) {
      return row.videoCount || 0
    }
    let totalCount = row.videoCount || 0
    for (const child of row.children) {
      totalCount += getTotalVideoCount(child)
    }
    return totalCount
  }

  getTableData()
</script>

<style scoped>
  .dialog-footer {
    padding: 20px;
    text-align: right;
  }
</style>
