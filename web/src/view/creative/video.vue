<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="标题">
          <el-input v-model="searchInfo.title" placeholder="请输入视频标题" />
        </el-form-item>
        <el-form-item label="分类">
          <el-cascader
            v-model="searchInfo.categoryIds"
            :options="categoryOptions"
            :props="{ checkStrictly: true, emitPath: true, value: 'ID', label: 'name' }"
            placeholder="请选择分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchInfo.status" placeholder="请选择状态" clearable>
            <el-option :value="0" label="待发布" />
            <el-option :value="1" label="已发布" />
            <el-option :value="2" label="生成中" />
            <el-option :value="3" label="发布失败" />
            <el-option :value="4" label="发布中" />
          </el-select>
        </el-form-item>
        <el-form-item label="带货信息">
          <el-select v-model="searchInfo.hasPromotion" placeholder="请选择" clearable>
            <el-option :value="true" label="有带货信息" />
            <el-option :value="false" label="无带货信息" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button type="primary" @click="openUploadDialog">新增发布视频</el-button>
        </el-form-item>
      </el-form>

      <div class="view-toggle-top">
        <el-radio-group v-model="viewMode" size="default">
          <el-radio-button label="table">表格视图</el-radio-button>
          <el-radio-button label="card">卡片视图</el-radio-button>
        </el-radio-group>
        <div class="batch-actions" v-if="viewMode === 'table'">
          <el-button
            type="danger"
            :disabled="!selectedVideos || selectedVideos.length === 0"
            @click="batchDeleteVideos"
          >
            批量删除 ({{ selectedVideos ? selectedVideos.length : 0 }})
          </el-button>
          <el-button type="warning" :disabled="!tableData || tableData.length === 0" @click="deleteCurrentPage">
            删除当页
          </el-button>
        </div>
      </div>
    </div>

    <div class="video-card-view" v-if="viewMode === 'card'">
      <div class="batch-select-header" v-if="tableData && tableData.length > 0">
        <el-checkbox v-model="selectAll" :indeterminate="isIndeterminate" @change="handleSelectAllChange">
          全选当页
        </el-checkbox>
        <span class="select-count">已选择 {{ selectedVideos ? selectedVideos.length : 0 }} 项</span>
      </div>
      <el-row :gutter="20">
        <el-col
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          v-for="(item, index) in tableData"
          :key="index"
          class="video-card-col"
        >
          <el-card
            shadow="hover"
            class="video-card"
            :class="{ selected: selectedVideos && selectedVideos.includes(item.id) }"
          >
            <div class="card-select-box">
              <el-checkbox
                :model-value="selectedVideos && selectedVideos.includes(item.id)"
                @change="(checked) => handleCardSelectChange(item.id, checked)"
              />
            </div>
            <div class="video-cover" @click="playVideo(item)">
              <el-image :src="item.cover" fit="contain">
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><picture-filled /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="play-icon">
                <el-icon><video-play /></el-icon>
              </div>
            </div>
            <div class="video-info">
              <h3 class="video-title" :title="item.title">{{ item.title }}</h3>
              <div class="video-meta">
                <el-tag size="small" :type="getStatusType(item.status)" class="status-tag">
                  <span class="tag-content">
                    <img v-if="item.status !== 2" src="@/assets/icons/icon_dy.png" class="douyin-icon" alt="抖音" />
                    <el-icon v-if="item.status === 2" class="loading-icon">
                      <loading />
                    </el-icon>
                    <span>{{ getStatusText(item.status) }}</span>
                  </span>
                </el-tag>
                <el-tag size="small" :type="item.source === 1 ? 'info' : 'success'" class="source-tag">
                  <span class="tag-content">
                    <el-icon v-if="item.source === 1" class="source-icon">
                      <upload />
                    </el-icon>
                    <el-icon v-else class="source-icon">
                      <cpu />
                    </el-icon>
                    <span>{{ item.source === 1 ? '手动上传' : 'AI生成' }}</span>
                  </span>
                </el-tag>
                <span class="category-name">{{ item.categoryName }}</span>
                <span v-if="item.duration" class="video-duration">
                  <el-icon class="duration-icon"><timer /></el-icon>
                  {{ formatDuration(item.duration) }}
                </span>
              </div>
              <div class="video-topics" v-if="item.topic">
                <el-tag
                  v-for="(topic, index) in JSON.parse(item.topic || '[]')"
                  :key="index"
                  size="small"
                  type="warning"
                  class="topic-tag"
                >
                  #{{ topic }}
                </el-tag>
              </div>
              <div class="video-promotion" v-if="item.manualProduct">
                <div class="promotion-label">带货信息:</div>
                <div
                  v-if="item.manualProduct.promotionTitle"
                  class="card-promotion-title"
                  :title="item.manualProduct.promotionTitle"
                >
                  {{ item.manualProduct.promotionTitle }}
                </div>
                <div class="card-promotion-links">
                  <div v-if="item.manualProduct.promotionUrl" class="card-promotion-url">
                    <div class="promotion-link-row">
                      <a :href="item.manualProduct.promotionUrl" target="_blank" class="card-url-link">
                        <el-icon class="cart-icon"><shopping-cart /></el-icon>
                        <span class="link-text">购物车链接</span>
                        <el-tag v-if="item.manualProduct.isAlliance" type="success" size="small" class="alliance-tag"
                          >团</el-tag
                        >
                      </a>
                      <el-button
                        type="primary"
                        link
                        size="small"
                        @click="copyToClipboard(item.manualProduct.promotionUrl)"
                        class="copy-btn"
                      >
                        <el-icon><copy-document /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  <div v-if="item.manualProduct.promotionUrl2" class="card-promotion-url">
                    <div class="promotion-link-row">
                      <a :href="item.manualProduct.promotionUrl2" target="_blank" class="card-url-link">
                        <el-icon class="cart-icon"><shopping-cart /></el-icon>
                        <span class="link-text">购物车链接2</span>
                        <el-tag v-if="item.manualProduct.isAlliance2" type="success" size="small" class="alliance-tag"
                          >团</el-tag
                        >
                      </a>
                      <el-button
                        type="primary"
                        link
                        size="small"
                        @click="copyToClipboard(item.manualProduct.promotionUrl2)"
                        class="copy-btn"
                      >
                        <el-icon><copy-document /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="video-time">{{ formatDate(item.createdAt) }}</div>
              <div class="video-actions">
                <el-button type="primary" link @click="editVideo(item)">
                  <el-icon>
                    <edit />
                  </el-icon>
                  编辑
                </el-button>
                <el-button type="primary" link @click="changeCover(item)">
                  <el-icon>
                    <picture-filled />
                  </el-icon>
                  更换封面
                </el-button>
                <el-button type="danger" link @click="deleteVideo(item)">
                  <el-icon>
                    <delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="video-table-view" v-else>
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" :selectable="() => true" />
        <el-table-column prop="title" label="视频标题" min-width="250">
          <template #default="scope">
            <div class="table-title-cell">
              <el-image class="table-thumb" :src="scope.row.cover" fit="contain" @click="playVideo(scope.row)">
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><picture-filled /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="table-title-info">
                <span class="table-title">{{ scope.row.title }}</span>
                <div v-if="scope.row.duration" class="table-duration-under-title">
                  <el-icon class="duration-icon"><timer /></el-icon>
                  {{ formatDuration(scope.row.duration) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="分类" width="140" />
        <el-table-column prop="sourceStatus" label="来源/状态" width="140">
          <template #default="scope">
            <div class="source-status-cell">
              <el-tag size="small" :type="scope.row.source === 1 ? 'success' : 'info'" class="source-tag">
                <span class="tag-content">
                  <el-icon v-if="scope.row.source === 1" class="source-icon">
                    <cpu />
                  </el-icon>
                  <el-icon v-else class="source-icon">
                    <upload />
                  </el-icon>
                  <span>{{ scope.row.source === 1 ? 'AI生成' : '手动上传' }}</span>
                </span>
              </el-tag>
              <el-tag size="small" :type="getStatusType(scope.row.status)" class="status-tag">
                <span class="tag-content">
                  <img v-if="scope.row.status !== 2" src="@/assets/icons/icon_dy.png" class="douyin-icon" alt="抖音" />
                  <el-icon v-if="scope.row.status === 2" class="loading-icon">
                    <loading />
                  </el-icon>
                  <span>{{ getStatusText(scope.row.status) }}</span>
                </span>
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="topic" label="话题" width="200">
          <template #default="scope">
            <div class="table-topics">
              <el-tag
                v-for="(topic, index) in JSON.parse(scope.row.topic || '[]')"
                :key="index"
                size="small"
                type="warning"
                class="topic-tag"
              >
                #{{ topic }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="promotionInfo" label="带货信息" min-width="200">
          <template #default="scope">
            <div class="promotion-info" v-if="scope.row.manualProduct">
              <div
                v-if="scope.row.manualProduct.promotionTitle"
                class="promotion-title"
                :title="scope.row.manualProduct.promotionTitle"
              >
                {{ scope.row.manualProduct.promotionTitle }}
              </div>
              <div class="promotion-links">
                <div v-if="scope.row.manualProduct.promotionUrl" class="promotion-url">
                  <div class="table-promotion-link-row">
                    <a :href="scope.row.manualProduct.promotionUrl" target="_blank" class="url-link">
                      <el-icon class="cart-icon"><shopping-cart /></el-icon>
                      <span class="link-text">购物车链接</span>
                      <el-tag v-if="scope.row.manualProduct.isAlliance" type="success" size="small" class="alliance-tag"
                        >团</el-tag
                      >
                    </a>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="copyToClipboard(scope.row.manualProduct.promotionUrl)"
                      class="copy-btn"
                    >
                      <el-icon><copy-document /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div v-if="scope.row.manualProduct.promotionUrl2" class="promotion-url">
                  <div class="table-promotion-link-row">
                    <a :href="scope.row.manualProduct.promotionUrl2" target="_blank" class="url-link">
                      <el-icon class="cart-icon"><shopping-cart /></el-icon>
                      <span class="link-text">购物车链接2</span>
                      <el-tag
                        v-if="scope.row.manualProduct.isAlliance2"
                        type="success"
                        size="small"
                        class="alliance-tag"
                        >团</el-tag
                      >
                    </a>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="copyToClipboard(scope.row.manualProduct.promotionUrl2)"
                      class="copy-btn"
                    >
                      <el-icon><copy-document /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            <span v-else class="no-promotion">无带货信息</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200">
          <template #default="scope">
            <div class="table-actions">
              <el-button type="primary" link @click="playVideo(scope.row)">
                <el-icon><video-play /></el-icon>
                播放
              </el-button>
              <el-button type="primary" link @click="editVideo(scope.row)">
                <el-icon>
                  <edit />
                </el-icon>
                编辑
              </el-button>
              <el-button type="primary" link @click="changeCover(scope.row)">
                <el-icon>
                  <picture-filled />
                </el-icon>
                更换封面
              </el-button>
              <el-button type="primary" link v-if="scope.row.status == 0" @click="handlePublishVideo(scope.row)">
                <el-icon>
                  <caret-right />
                </el-icon>
                立即发布
              </el-button>
              <el-button type="danger" link @click="deleteVideo(scope.row)">
                <el-icon>
                  <delete />
                </el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 上传视频对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      :title="form.id ? '编辑视频' : '新增发布视频'"
      width="800px"
      destroy-on-close
    >
      <el-form :model="form" label-width="80px" ref="uploadForm" :rules="formRules">
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入视频标题" />
        </el-form-item>

        <el-form-item label="视频分类" prop="categoryId">
          <el-cascader
            v-model="form.categoryId"
            :options="categoryOptions"
            :props="{
              checkStrictly: true,
              emitPath: false,
              value: 'ID',
              label: 'name',
              children: 'children',
              expandTrigger: 'click'
            }"
            placeholder="请选择分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item v-if="form.status == 3 || form.status == 0" label="修改状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option :value="0" label="待发布" />
            <el-option :value="3" label="发布失败" disabled />
          </el-select>
        </el-form-item>
        <el-form-item label="视频话题">
          <div class="topic-search">
            <el-button type="primary" link @click="searchTopics" :loading="searchingTopics">
              <el-icon>
                <search />
              </el-icon>
              根据标题搜索话题
            </el-button>
            <el-button type="primary" link @click="showTopicInputDialog">
              <el-icon>
                <edit />
              </el-icon>
              手动输入话题
            </el-button>
          </div>
          <el-select
            v-model="form.topic"
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择话题（最多5个）"
            :max-collapse-tags="3"
            :multiple-limit="5"
          >
            <el-option
              v-for="topic in topicOptions"
              :key="topic.challenge_info.cid"
              :label="topic.challenge_info.challenge_name"
              :value="topic.challenge_info.cha_name"
            >
              <div class="topic-option">
                <span>{{ topic.challenge_info.cha_name }}</span>
                <div class="topic-stats">
                  <el-tag size="small" type="info">{{ formatNumber(topic.challenge_info.user_count) }}人参与</el-tag>
                  <el-tag size="small" type="info">{{ formatNumber(topic.challenge_info.view_count) }}次浏览</el-tag>
                </div>
              </div>
            </el-option>
            <el-option v-for="topic in customTopics" :key="'custom-' + topic" :label="topic" :value="topic">
              <div class="topic-option">
                <span>{{ topic }}</span>
                <div class="topic-stats">
                  <el-tag size="small" type="success">自定义话题</el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="带货商品">
          <el-select
            v-model="form.manualProductId"
            placeholder="请选择带货商品"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option v-for="product in validProductList" :key="product.ID" :label="product.title" :value="product.ID">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <span>{{ product.title }}</span>
                <el-tag v-if="product.isAlliance" type="success" size="small">团</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="视频封面" prop="cover">
          <el-upload
            class="cover-uploader"
            :show-file-list="false"
            action="#"
            :http-request="uploadCover"
            :before-upload="beforeCoverUpload"
            drag
          >
            <div v-if="coverUrl" class="cover-preview-container">
              <img :src="coverUrl" class="cover" />
            </div>
            <template v-else>
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">拖拽图片到此处，或 <em>点击上传</em></div>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                封面图片为可选项，若不上传将自动提取视频第一帧作为封面。支持任意比例图片，将完整显示。建议使用jpg/png格式。
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="视频文件" prop="video">
          <div class="video-upload-preview-container">
            <div class="video-upload-container">
              <el-upload
                class="video-uploader"
                :show-file-list="true"
                :limit="1"
                action="#"
                :http-request="uploadVideo"
                :before-upload="beforeVideoUpload"
                :on-remove="handleVideoRemove"
                :file-list="videoFileList"
                drag
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  {{ form.id ? '上传新视频将替换现有视频' : '拖拽视频到此处，或点击上传' }}
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    {{ form.id ? '若不上传新视频，将保持原视频不变' : '视频文件支持mp4格式，大小不超过200MB' }}
                  </div>
                </template>
              </el-upload>
              <!-- 编辑模式下显示当前视频提示 -->
              <div v-if="form.id && !videoFile" class="current-video-info">
                <el-tag type="info">当前视频将保持不变</el-tag>
              </div>
            </div>

            <!-- 视频预览区域 -->
            <div v-if="videoFile || (form.id && previewVideoUrl)" class="video-preview-wrapper">
              <div class="video-preview-container">
                <div class="video-preview-title">视频预览</div>
                <video
                  ref="previewVideoRef"
                  :src="previewVideoUrl || (videoFile ? URL.createObjectURL(videoFile) : '')"
                  class="video-preview"
                  controls
                  @loadstart="previewVideoLoading = true"
                  @canplay="previewVideoLoading = false"
                ></video>
                <div v-if="previewVideoLoading" class="preview-loading">
                  <el-icon class="loading-icon">
                    <loading />
                  </el-icon>
                  <span>视频加载中...</span>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-progress v-if="uploadProgress > 0" :percentage="uploadProgress" :format="(percent) => percent + '%'" />
          <div class="dialog-actions">
            <el-button @click="uploadDialogVisible = false" :disabled="uploading">取消</el-button>
            <el-button type="primary" @click="submitUpload" :loading="uploading">
              {{ uploading ? '上传中...' : '提交' }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 视频播放器对话框 -->
    <el-dialog
      v-model="playerDialogVisible"
      :fullscreen="true"
      :destroy-on-close="true"
      class="video-player-dialog"
      :show-close="false"
    >
      <div class="video-player-container">
        <div v-if="videoLoading" class="video-loading">
          <el-icon class="loading-icon">
            <loading />
          </el-icon>
          <div class="loading-text">视频加载中...</div>
        </div>
        <div class="video-wrapper">
          <video
            :src="currentVideoUrl"
            ref="videoPlayer"
            controls
            class="video-player"
            @loadstart="videoLoading = true"
            @canplay="videoLoading = false"
            @error="handleVideoError"
          ></video>
        </div>

        <!-- 添加视频信息悬浮卡片 -->
        <div class="video-info-card" v-if="!videoLoading">
          <h3>{{ currentVideoTitle }}</h3>
          <div class="video-meta-info">
            <span v-if="currentVideoCategory"
              ><el-tag size="small" type="info">{{ currentVideoCategory }}</el-tag></span
            >
            <span class="video-time">{{ formatDate(new Date()) }}</span>
          </div>
        </div>

        <!-- 添加自定义控制栏 -->
        <div class="custom-controls" v-if="!videoLoading">
          <el-button type="primary" circle @click="toggleFullscreen">
            <el-icon><full-screen /></el-icon>
          </el-button>
          <el-button type="danger" circle @click="playerDialogVisible = false">
            <el-icon>
              <close />
            </el-icon>
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 更换封面对话框 -->
    <el-dialog v-model="coverDialogVisible" title="更换封面" width="600px">
      <div class="cover-comparison">
        <div class="cover-item">
          <h4>原封面</h4>
          <div class="cover-container">
            <el-image :src="currentCoverUrl" fit="contain" class="cover">
              <template #error>
                <div class="image-placeholder">
                  <el-icon><picture-filled /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
        </div>
        <div class="cover-item">
          <h4>新封面</h4>
          <div class="cover-container">
            <el-upload
              class="cover-uploader"
              :show-file-list="false"
              action="#"
              :http-request="changeCoverUpload"
              :before-upload="beforeCoverUpload"
              drag
            >
              <div v-if="newCoverUrl" class="cover-preview-container">
                <img :src="newCoverUrl" class="cover" />
              </div>
              <template v-else>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">拖拽图片到此处，或 <em>点击上传</em></div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="coverDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitChangeCover" :loading="changingCover">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 手动输入话题对话框 -->
    <el-dialog v-model="topicInputDialogVisible" title="手动输入话题" width="500px">
      <div class="custom-topic-input">
        <el-form :model="topicInputForm" ref="topicInputFormRef">
          <el-form-item>
            <div class="topic-input-tip">请输入话题，每行一个，最多5个</div>
            <el-input
              type="textarea"
              v-model="topicInputForm.topics"
              :rows="5"
              placeholder="请输入话题，每行输入一个话题，不需要添加#号"
            ></el-input>
          </el-form-item>
          <div class="topic-preview" v-if="topicInputPreview.length > 0">
            <div class="preview-label">预览：</div>
            <div class="topic-tags-preview">
              <el-tag
                v-for="(topic, index) in topicInputPreview"
                :key="index"
                type="warning"
                size="small"
                class="topic-tag"
              >
                #{{ topic }}
              </el-tag>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="topicInputDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addCustomTopics">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 立即发布视频对话框 -->
    <el-dialog v-model="publishDialogVisible" title="立即发布" width="30%">
      <el-form :model="publishForm" label-width="100px">
        <el-form-item label="抖音号" v-model="publishForm.uniqueId" style="display: flex; align-items: center">
          <UniqueIdMatcher style="flex: 1" @select-user="onUserSelected" />
        </el-form-item>
        <!-- 视频标题 -->
        <el-form-item label="视频标题">
          <el-input v-model="publishForm.title" disabled="" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePublishDialog">取消</el-button>
          <el-button type="primary" @click="submitPublish">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    UploadFilled,
    VideoPlay,
    Edit,
    PictureFilled,
    Delete,
    FullScreen,
    Search,
    Close,
    CopyDocument,
    Timer,
    Upload,
    Cpu,
    ShoppingCart,
    CaretRight
  } from '@element-plus/icons-vue'
  import { formatDate } from '@/utils/format'
  import {
    getVideoList,
    createVideo,
    deleteVideo as removeVideo,
    setVideoCover,
    searchTopics as searchTopicsApi,
    publishVideo
  } from '@/api/creative/video'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import { getProductManualList } from '@/api/douyin/product'
  import { useRoute } from 'vue-router'
  import UniqueIdMatcher from '@/components/douyin/uniqueIdMatcher.vue'

  const route = useRoute()

  // 表格数据
  const tableData = ref([])
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const loading = ref(false)
  const searchInfo = ref({
    title: '',
    categoryId: null,
    categoryIds: [],
    status: null,
    hasPromotion: null
  })

  // 分类选项
  const categoryOptions = ref([])

  // 商品列表
  const productList = ref([])

  // 上传表单
  const uploadForm = ref(null)
  const uploadDialogVisible = ref(false)
  const coverUrl = ref('')
  const coverFile = ref(null)
  const videoFile = ref(null)
  const videoFileList = ref([])
  const uploading = ref(false)
  const uploadProgress = ref(0)
  const form = ref({
    id: null,
    title: '',
    categoryId: null,
    topic: [],
    status: null,
    manualProductId: null
  })

  // 视频播放器
  const playerDialogVisible = ref(false)
  const currentVideoUrl = ref('')
  const currentVideoTitle = ref('')
  const currentVideoCategory = ref('')
  const videoPlayer = ref(null)
  const videoLoading = ref(false)
  const isFullscreen = ref(false)

  // 更换封面
  const coverDialogVisible = ref(false)
  const newCoverUrl = ref('')
  const newCoverFile = ref(null)
  const changingCover = ref(false)
  const currentVideoId = ref(null)
  const currentCoverUrl = ref('')

  // 视频预览
  const previewVideoUrl = ref('')
  const previewVideoLoading = ref(false)
  const originalVideoUrl = ref('')

  // 话题相关
  const topicOptions = ref([])
  const searchingTopics = ref(false)
  const customTopics = ref([])

  // 手动输入话题对话框
  const topicInputDialogVisible = ref(false)
  const topicInputForm = ref({
    topics: ''
  })
  const topicInputFormRef = ref(null)

  // 立即发布视频对话框
  const publishDialogVisible = ref(false)
  const publishForm = ref({
    uniqueId: null,
    videoId: null,
    title: null
  })
  const currentPublishVideo = ref(null)
  const selectedUserInfo = ref(null)

  // 批量选择相关变量
  const selectedVideos = ref([])
  const selectAll = ref(false)
  const isIndeterminate = computed(() => {
    // 添加空值检查
    if (!selectedVideos.value || !tableData.value) {
      return false
    }
    return selectedVideos.value.length > 0 && selectedVideos.value.length < tableData.value.length
  })

  // 自定义话题预览
  const topicInputPreview = computed(() => {
    if (!topicInputForm.value.topics) return []
    return topicInputForm.value.topics
      .split('\n')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0)
      .slice(0, 5)
  })

  // 有效的商品列表
  const validProductList = computed(() => {
    return productList.value.filter((product) => product && product.ID && product.title)
  })

  // 表单验证规则
  const formRules = {
    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
    video: [
      {
        required: true,
        validator: (rule, value, callback) => {
          // 编辑模式下不强制要求上传视频
          if (form.value.id) {
            callback()
          } else if (!videoFile.value) {
            callback(new Error('请上传视频'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  }

  // 视频列表视图模式
  const viewMode = ref('card') // 'table' or 'card'

  // 监听视图模式变化并保存到本地存储
  watch(viewMode, (newVal) => {
    localStorage.setItem('videoViewMode', newVal)
  })

  // 监听选择状态变化，同步全选状态
  watch(
    [selectedVideos, tableData],
    ([selected, data]) => {
      // 添加空值检查
      if (!data || !selected) {
        selectAll.value = false
        return
      }
      if (data.length === 0) {
        selectAll.value = false
      } else {
        selectAll.value = selected.length === data.length
      }
    },
    { deep: true }
  )

  // 获取分类列表
  const getCategoryOptions = async () => {
    try {
      const res = await getVideoCategoryList({ page: 1, pageSize: 10 })
      if (res.code === 0) {
        categoryOptions.value = res.data.list
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('获取分类列表失败')
    }
  }

  // 获取商品列表
  const getProductList = async () => {
    try {
      const res = await getProductManualList({ page: 1, pageSize: 10 })
      if (res.code === 0) {
        productList.value = res.data.list || []
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('获取商品列表失败')
    }
  }

  // 获取视频列表
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await getVideoList({
        page: page.value,
        pageSize: pageSize.value,
        ...searchInfo.value
      })
      if (res.code === 0) {
        tableData.value = res.data.list || []
        total.value = res.data.total
        page.value = res.data.page
        pageSize.value = res.data.pageSize
        // 清空选择状态
        selectedVideos.value = []
        selectAll.value = false
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('获取视频列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const onSubmit = () => {
    page.value = 1
    pageSize.value = 10
    if (searchInfo.value.categoryIds && searchInfo.value.categoryIds.length > 0) {
      searchInfo.value.categoryId = searchInfo.value.categoryIds[searchInfo.value.categoryIds.length - 1]
    } else {
      searchInfo.value.categoryId = null
    }
    getTableData()
  }

  // 上传视频对话框
  const openUploadDialog = () => {
    form.value = {
      id: null,
      title: '',
      categoryId: null,
      topic: [],
      manualProductId: null
    }
    coverUrl.value = ''
    coverFile.value = null
    videoFile.value = null
    videoFileList.value = []
    customTopics.value = []
    uploadDialogVisible.value = true
  }

  // 封面上传前校验
  const beforeCoverUpload = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
      ElMessage.error('封面只能是图片格式!')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('封面大小不能超过 2MB!')
      return false
    }
    return true
  }

  // 视频上传前校验
  const beforeVideoUpload = (file) => {
    const isMP4 = file.type === 'video/mp4'
    const isLt200M = file.size / 1024 / 1024 < 200

    if (!isMP4) {
      ElMessage.error('视频只能是 MP4 格式!')
      return false
    }
    if (!isLt200M) {
      ElMessage.error('视频大小不能超过 200MB!')
      return false
    }
    return true
  }

  // 上传封面
  const uploadCover = (options) => {
    coverFile.value = options.file
    const reader = new FileReader()
    reader.onload = (e) => {
      coverUrl.value = e.target.result
    }
    reader.readAsDataURL(options.file)
  }

  // 上传视频
  const uploadVideo = (options) => {
    // 清除原有的视频状态标记
    videoFile.value = options.file
    // 更新文件列表
    videoFileList.value = [{ name: options.file.name, size: options.file.size }]

    // 创建视频预览URL
    if (previewVideoUrl.value && previewVideoUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(previewVideoUrl.value)
    }
    previewVideoUrl.value = URL.createObjectURL(options.file)
    // 打印上传的视频信息用于调试
    console.log('上传新视频:', options.file.name, options.file.size)
    uploadForm.value.validateField('video')
  }

  // 提交上传
  const submitUpload = async () => {
    if (!uploadForm.value) return

    await uploadForm.value.validate(async (valid) => {
      if (valid && (videoFile.value || form.value.id)) {
        uploading.value = true
        uploadProgress.value = 0

        try {
          const formData = new FormData()
          formData.append('title', form.value.title)
          formData.append('categoryId', form.value.categoryId)
          formData.append('topic', JSON.stringify(form.value.topic))
          if (form.value.manualProductId) {
            formData.append('manualProductId', form.value.manualProductId)
          }
          if (form.value.status !== null && form.value.status !== undefined) {
            formData.append('status', form.value.status)
          } else {
            // 创建视频，改成待发布状态
            formData.append('status', 0)
          }

          // 如果是编辑模式，添加ID
          if (form.value.id) {
            formData.append('id', form.value.id)
          }

          if (coverFile.value) {
            formData.append('cover', coverFile.value)
          }

          // 判断是否是编辑模式且没有新的视频文件
          if (videoFile.value) {
            // 上传了新视频
            console.log('上传新视频:', videoFile.value.name)
            formData.append('video', videoFile.value)
          } else if (form.value.id) {
            // 编辑模式但没有上传新视频，明确标记不更新视频
            console.log('编辑模式，不更新视频')
            formData.append('videoUpdate', 'false')
            // 创建一个极小的空文件用作占位符
            const emptyBlob = new Blob(['placeholder'], { type: 'text/plain' })
            formData.append('video', emptyBlob, 'no_update.txt')
          } else {
            // 这种情况不应该发生，因为新增模式必须有视频文件
            ElMessage.warning('请上传视频文件')
            uploading.value = false
            return
          }

          // 模拟上传进度
          const progressInterval = setInterval(() => {
            if (uploadProgress.value < 90) {
              uploadProgress.value += 10
            }
          }, 1000)

          console.log('提交表单...', formData)
          const res = await createVideo(formData)
          clearInterval(progressInterval)
          uploadProgress.value = 100

          if (res.code === 0) {
            setTimeout(() => {
              ElMessage.success(form.value.id ? '视频更新成功' : '视频上传成功')
              uploadDialogVisible.value = false
              getTableData()
              uploadProgress.value = 0
              // 清空相关状态
              videoFile.value = null
              videoFileList.value = []
              if (previewVideoUrl.value && previewVideoUrl.value.startsWith('blob:')) {
                URL.revokeObjectURL(previewVideoUrl.value)
                previewVideoUrl.value = ''
              }
            }, 500)
          } else {
            ElMessage.error(res.message || '操作失败')
          }
        } catch (err) {
          console.error(err)
          ElMessage.error(form.value.id ? '视频更新失败' : '视频上传失败')
        } finally {
          uploading.value = false
        }
      } else {
        if (!videoFile.value && !form.value.id) {
          ElMessage.warning('请上传视频文件')
        } else {
          ElMessage.warning('请填写完整信息')
        }
      }
    })
  }

  // 播放视频
  const playVideo = (row) => {
    currentVideoUrl.value = row.url
    currentVideoTitle.value = row.title
    currentVideoCategory.value = row.categoryName
    playerDialogVisible.value = true

    // 监听弹窗关闭事件，停止视频播放
    watch(playerDialogVisible, (newVal) => {
      if (!newVal && videoPlayer.value) {
        videoPlayer.value.pause()
        videoPlayer.value.currentTime = 0
        isFullscreen.value = false
      }
    })

    // 等待DOM更新后设置视频尺寸
    setTimeout(() => {
      if (videoPlayer.value) {
        adjustVideoPlayerSize()
      }
    }, 300)
  }

  // 视频播放器尺寸调整
  const adjustVideoPlayerSize = () => {
    if (!videoPlayer.value) return

    const videoElement = videoPlayer.value
    const containerElement = videoElement.parentElement

    // 根据视频比例设置容器尺寸
    if (videoElement.videoWidth && videoElement.videoHeight) {
      const aspectRatio = videoElement.videoWidth / videoElement.videoHeight
      if (aspectRatio > 1) {
        // 横向视频
        containerElement.style.width = '90vw'
        containerElement.style.height = `${90 / aspectRatio}vw`
        containerElement.style.maxHeight = '80vh'
      } else {
        // 竖向视频
        containerElement.style.height = '80vh'
        containerElement.style.width = `${80 * aspectRatio}vh`
        containerElement.style.maxWidth = '90vw'
      }
    }
  }

  // 删除视频
  const deleteVideo = (row) => {
    ElMessageBox.confirm('确定要删除该视频吗？此操作不可恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await removeVideo({ id: row.id })
          if (res.code === 0) {
            ElMessage.success('删除成功')
            if (tableData.value && tableData.value.length === 1 && page.value > 1) {
              page.value--
            }
            getTableData()
          } else {
            ElMessage.error(res.message || '删除失败')
          }
        } catch (err) {
          console.error(err)
          ElMessage.error('删除失败')
        }
      })
      .catch(() => {})
  }

  // 更换封面
  const changeCover = (row) => {
    currentVideoId.value = row.id
    newCoverUrl.value = ''
    newCoverFile.value = null
    currentCoverUrl.value = row.cover
    coverDialogVisible.value = true
  }

  // 上传新封面
  const changeCoverUpload = (options) => {
    newCoverFile.value = options.file
    const reader = new FileReader()
    reader.onload = (e) => {
      newCoverUrl.value = e.target.result
    }
    reader.readAsDataURL(options.file)
  }

  // 提交更换封面
  const submitChangeCover = async () => {
    if (!newCoverFile.value || !currentVideoId.value) {
      ElMessage.warning('请先选择新封面')
      return
    }

    changingCover.value = true
    try {
      const formData = new FormData()
      formData.append('id', currentVideoId.value)
      formData.append('cover', newCoverFile.value)

      const res = await setVideoCover(formData)
      if (res.code === 0) {
        ElMessage.success('封面更新成功')
        coverDialogVisible.value = false
        getTableData()
      } else {
        ElMessage.error(res.message || '更新失败')
      }
    } catch (err) {
      console.error(err)
      ElMessage.error('更新封面失败')
    } finally {
      changingCover.value = false
    }
  }

  // 分页
  const handleSizeChange = (val) => {
    pageSize.value = val
    getTableData()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    getTableData()
  }

  // 处理视频移除
  const handleVideoRemove = () => {
    console.log('移除视频文件')
    videoFile.value = null
    videoFileList.value = []
    // 释放预览URL
    if (previewVideoUrl.value && previewVideoUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(previewVideoUrl.value)
      previewVideoUrl.value = form.value.id ? originalVideoUrl.value : ''
    }
    uploadForm.value.validateField('video')
  }

  // 编辑视频
  const editVideo = (row) => {
    form.value = {
      id: row.id,
      title: row.title,
      categoryId: row.categoryId,
      status: row.status,
      topic: row.topic ? JSON.parse(row.topic) : [],
      manualProductId: row.manualProductId
    }
    coverUrl.value = row.cover
    // 设置原始视频URL用于预览
    previewVideoUrl.value = row.url
    originalVideoUrl.value = row.url

    // 清空视频文件和文件列表
    videoFile.value = null
    videoFileList.value = []

    // 处理自定义话题
    if (row.topic) {
      const topics = JSON.parse(row.topic)
      if (topics && topics.length > 0) {
        customTopics.value = topics
      }
    } else {
      customTopics.value = []
    }

    uploadDialogVisible.value = true
  }

  // 处理视频加载错误
  const handleVideoError = () => {
    videoLoading.value = false
    ElMessage.error('视频加载失败，请检查网络连接或视频地址')
  }

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!videoPlayer.value) return

    if (!document.fullscreenElement) {
      videoPlayer.value
        .requestFullscreen()
        .then(() => {
          isFullscreen.value = true
        })
        .catch((err) => {
          ElMessage.warning('无法进入全屏模式: ' + err.message)
        })
    } else {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }

  // 状态处理函数
  const getStatusType = (status) => {
    switch (status) {
      case 0:
        return 'warning' // 待发布
      case 1:
        return 'success' // 已发布
      case 2:
        return 'info' // 生成中
      case 3:
        return 'danger' // 发布失败
      case 4:
        return 'primary' // 发布中
      default:
        return 'info'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 0:
        return '待发布'
      case 1:
        return '已发布'
      case 2:
        return '生成中'
      case 3:
        return '发布失败'
      case 4:
        return '发布中'
      default:
        return '未知状态'
    }
  }

  // 格式化数字
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    }
    return num
  }

  // 搜索话题
  const searchTopics = async () => {
    if (!form.value.title) {
      ElMessage.warning('请先输入视频标题')
      return
    }

    searchingTopics.value = true
    try {
      // 新接口使用 keywords 数组参数
      const res = await searchTopicsApi({ keywords: [form.value.title] })
      if (res.code === 0 && res.data && Array.isArray(res.data)) {
        // 取第一个搜索结果（因为只搜索了一个关键词）
        const searchResult = res.data[0]

        if (searchResult && searchResult.data && Array.isArray(searchResult.data.challenge_list)) {
          topicOptions.value = searchResult.data.challenge_list
          if (topicOptions.value.length === 0) {
            ElMessage.info('未找到相关话题')
          } else {
            // 自动选择前5个话题
            form.value.topic = topicOptions.value.slice(0, 5).map((topic) => topic.challenge_info.cha_name)
            ElMessage.success(`找到 ${topicOptions.value.length} 个相关话题，已自动选择前5个`)
          }
        } else {
          topicOptions.value = []
          ElMessage.info('未找到相关话题')
        }
      } else {
        topicOptions.value = []
        ElMessage.warning('搜索话题失败，请稍后重试')
      }
    } catch (err) {
      console.error(err)
      topicOptions.value = []
      ElMessage.error('搜索话题失败')
    } finally {
      searchingTopics.value = false
    }
  }

  // 显示手动输入话题对话框
  const showTopicInputDialog = () => {
    // 如果有已选择的话题，填充到输入框中
    if (form.value.topic && form.value.topic.length > 0) {
      topicInputForm.value.topics = form.value.topic.join('\n')
    } else {
      topicInputForm.value.topics = ''
    }
    topicInputDialogVisible.value = true
  }

  // 添加自定义话题
  const addCustomTopics = () => {
    const topics = topicInputForm.value.topics
      .split('\n')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0)

    if (topics.length > 5) {
      ElMessage.warning('最多只能添加5个话题')
      return
    }

    // 更新自定义话题列表
    customTopics.value = topics

    // 更新表单中的话题选择
    form.value.topic = topics

    topicInputDialogVisible.value = false
    ElMessage.success('已添加自定义话题')
  }

  // 递归查找分类路径
  const findCategoryPath = (categoryId, categories) => {
    for (const category of categories) {
      if (category.ID === categoryId) {
        return [category.ID]
      }
      if (category.children) {
        const path = findCategoryPath(categoryId, category.children)
        if (path) {
          return [category.ID, ...path]
        }
      }
    }
    return null
  }

  // 初始化
  const init = () => {
    if (route.query.viewMode) {
      viewMode.value = route.query.viewMode
    } else {
      viewMode.value = localStorage.getItem('videoViewMode') || 'card'
    }

    getCategoryOptions()
    getProductList()

    getCategoryOptions().then(() => {
      if (route.query.categoryId) {
        const categoryId = Number(route.query.categoryId)
        const path = findCategoryPath(categoryId, categoryOptions.value)
        if (path) {
          searchInfo.value.categoryIds = path
        }
        searchInfo.value.categoryId = categoryId
      }
      if (route.query.status !== undefined) {
        searchInfo.value.status = Number(route.query.status)
      }
      getTableData()
    })
  }

  onMounted(() => {
    init()

    // 添加全屏变化监听
    document.addEventListener('fullscreenchange', () => {
      isFullscreen.value = !!document.fullscreenElement
    })
  })

  const handlePublishVideo = (item) => {
    console.log('处理发布视频:', item)
    currentPublishVideo.value = item
    publishDialogVisible.value = true
    publishForm.value.title = item.title
    publishForm.value.videoId = item.id
  }

  const closePublishDialog = () => {
    currentPublishVideo.value = null
    publishDialogVisible.value = false
    publishForm.value.uniqueId = null
    publishForm.value.videoId = null
    selectedUserInfo.value = null
  }

  const submitPublish = async () => {
    if (!publishForm.value.uniqueId) {
      ElMessage.warning('请选择发布的目标账号')
      return
    }

    const res = await publishVideo({
      uniqueId: selectedUserInfo.value.uniqueId,
      videoId: publishForm.value.videoId
    })
    if (res.code !== 0) {
      ElMessage.error(res.message || '发布失败')
      return
    }

    publishDialogVisible.value = false
    getTableData()
    ElMessage.success('发布请求已提交')
  }

  const onUserSelected = (user) => {
    selectedUserInfo.value = user
    publishForm.value.uniqueId = selectedUserInfo.value.uniqueId
  }

  const handleSelectAllChange = () => {
    if (selectAll.value) {
      // 添加空值检查
      selectedVideos.value = tableData.value ? tableData.value.map((item) => item.id) : []
    } else {
      selectedVideos.value = []
    }
  }

  const handleCardSelectChange = (itemId, checked) => {
    if (checked) {
      if (!selectedVideos.value.includes(itemId)) {
        selectedVideos.value.push(itemId)
      }
    } else {
      selectedVideos.value = selectedVideos.value.filter((id) => id !== itemId)
    }
  }

  const handleSelectionChange = (selectedRows) => {
    // 添加空值检查
    selectedVideos.value = selectedRows ? selectedRows.map((row) => row.id) : []
  }

  // 批量删除函数
  const batchDeleteVideos = () => {
    if (!selectedVideos.value || selectedVideos.value.length === 0) {
      ElMessage.warning('请先选择要删除的视频')
      return
    }

    ElMessageBox.confirm(
      `确定要删除选中的 ${selectedVideos.value ? selectedVideos.value.length : 0} 个视频吗？此操作不可恢复`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(async () => {
        try {
          // 并发删除选中的视频
          const deletePromises = selectedVideos.value.map((id) => removeVideo({ id }))

          const results = await Promise.allSettled(deletePromises)

          // 统计删除结果
          const successful = results.filter((result) => result.status === 'fulfilled' && result.value.code === 0).length

          const failed = results.length - successful

          if (successful > 0) {
            ElMessage.success(`成功删除 ${successful} 个视频${failed > 0 ? `，${failed} 个删除失败` : ''}`)
          } else {
            ElMessage.error('删除失败')
          }

          // 清空选择并刷新数据
          selectedVideos.value = []
          selectAll.value = false

          // 如果当前页没有数据了，返回上一页
          if (tableData.value && tableData.value.length <= successful && page.value > 1) {
            page.value--
          }

          getTableData()
        } catch (err) {
          console.error(err)
          ElMessage.error('批量删除失败')
        }
      })
      .catch(() => {})
  }

  const deleteCurrentPage = () => {
    if (!tableData.value || tableData.value.length === 0) {
      ElMessage.warning('当前页没有数据')
      return
    }

    ElMessageBox.confirm(
      `确定要删除当前页的所有 ${tableData.value ? tableData.value.length : 0} 个视频吗？此操作不可恢复`,
      '删除当前页确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(async () => {
        try {
          // 获取当前页所有视频ID
          const currentPageIds = tableData.value ? tableData.value.map((item) => item.id) : []

          // 并发删除当前页所有视频
          const deletePromises = currentPageIds.map((id) => removeVideo({ id }))

          const results = await Promise.allSettled(deletePromises)

          // 统计删除结果
          const successful = results.filter((result) => result.status === 'fulfilled' && result.value.code === 0).length

          const failed = results.length - successful

          if (successful > 0) {
            ElMessage.success(`成功删除 ${successful} 个视频${failed > 0 ? `，${failed} 个删除失败` : ''}`)
          } else {
            ElMessage.error('删除失败')
          }

          // 清空选择
          selectedVideos.value = []
          selectAll.value = false

          // 如果当前页删除完了，返回上一页
          if (page.value > 1) {
            page.value--
          }

          getTableData()
        } catch (err) {
          console.error(err)
          ElMessage.error('删除当前页失败')
        }
      })
      .catch(() => {})
  }

  const copyToClipboard = (text) => {
    const input = document.createElement('input')
    input.value = text
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
    ElMessage.success('链接已复制到剪贴板')
  }

  // 格式化时长
  const formatDuration = (duration) => {
    if (!duration || duration === 0) return '00:00'

    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    const seconds = Math.floor(duration % 60)

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
        .toString()
        .padStart(2, '0')}`
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  }
</script>

<style scoped>
  .cover-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 100%;
  }

  .cover-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
  }

  .cover-uploader .el-upload-dragger {
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .cover-preview-container {
    width: 100%;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f8f8;
  }

  .cover {
    max-width: 100%;
    max-height: 160px;
    display: block;
    object-fit: contain;
    background-color: #f8f8f8;
  }

  .dialog-footer {
    padding: 20px;
    text-align: right;
  }

  .dialog-actions {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .video-player-container {
    width: 100%;
    height: 100vh;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .video-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .video-player {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    background-color: #000;
    object-fit: contain;
    z-index: 1;
  }

  .video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 3;
  }

  .loading-icon {
    animation: rotating 2s linear infinite;
    margin-right: 4px;
    font-size: 28px;
  }

  .loading-text {
    margin-top: 15px;
    font-size: 18px;
  }

  .video-player-dialog :deep(.el-dialog__body) {
    padding: 0;
    background-color: #000;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  .video-player-dialog :deep(.el-dialog__header) {
    display: none;
  }

  .video-player-dialog :deep(.el-dialog__footer) {
    display: none;
  }

  /* 新增视频信息卡片样式 */
  .video-info-card {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    max-width: 60%;
    z-index: 2;
    backdrop-filter: blur(8px);
    opacity: 1;
    transition: opacity 0.3s;
  }

  .video-player-container:hover .video-info-card {
    opacity: 1;
  }

  .video-info-card h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    line-height: 1.4;
  }

  .video-meta-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .video-time {
    color: #e0e0e0;
    font-size: 14px;
  }

  /* 自定义控制栏样式 */
  .custom-controls {
    position: absolute;
    bottom: 30px;
    right: 30px;
    display: flex;
    gap: 15px;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .video-player-container:hover .custom-controls {
    opacity: 1;
  }

  /* 旋转动画 */
  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .video-uploader .el-upload {
    width: 100%;
  }

  .video-uploader .el-upload-dragger {
    width: 100%;
    height: 180px;
  }

  .el-upload__text {
    margin-top: 10px;
    color: #606266;
  }

  .el-upload__text em {
    color: var(--el-color-primary);
    font-style: normal;
  }

  .el-icon--upload {
    font-size: 48px;
    color: #c0c4cc;
    margin-top: 40px;
  }

  .current-video-info {
    margin-top: 10px;
    padding: 5px 0;
  }

  /* 卡片视图样式 */
  .video-card-view {
    margin-bottom: 20px;
  }

  .video-card-col {
    margin-bottom: 20px;
  }

  .video-card {
    position: relative;
    transition: all 0.3s;
  }

  .video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .video-card.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  }

  .video-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid var(--el-color-primary);
    border-radius: 6px;
    pointer-events: none;
  }

  .video-cover {
    position: relative;
    height: 160px;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-cover .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 48px;
    opacity: 0;
    transition: opacity 0.3s;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-cover:hover .play-icon {
    opacity: 1;
  }

  .video-info {
    padding: 10px 0;
  }

  .video-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .video-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .source-tag {
    margin-left: 8px;
  }

  .status-tag {
    display: inline-flex;
    align-items: center;
  }

  .category-name {
    font-size: 13px;
    color: #606266;
  }

  .video-topics {
    margin: 8px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .topic-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }

  .table-topics {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 100%;
  }

  .table-topics .topic-tag {
    margin-bottom: 4px;
  }

  .video-promotion {
    margin: 8px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    border: 1px solid #e9ecef;
  }

  .promotion-label {
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .card-promotion-title {
    color: #303133;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
    padding: 4px 8px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
  }

  .card-promotion-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .promotion-link-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .promotion-link-row:hover {
    background-color: #e9ecef;
  }

  .card-url-link {
    color: #409eff;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    flex: 1;
  }

  .card-url-link:hover {
    color: #1890ff;
  }

  .link-text {
    font-weight: 500;
  }

  .alliance-tag {
    margin-left: 4px;
  }

  .copy-btn {
    padding: 6px !important;
    margin-left: 8px;
    background-color: rgba(64, 158, 255, 0.1);
    border-radius: 4px;
    transition: all 0.2s;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .copy-btn:hover {
    background-color: rgba(64, 158, 255, 0.2);
    transform: scale(1.05);
  }

  .copy-btn .el-icon {
    margin: 0;
  }

  .video-time {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }

  .video-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    align-items: center;
  }

  .video-actions .el-button {
    margin: 0;
    padding: 4px 8px;
    height: auto;
    min-height: 28px;
    display: inline-flex;
    align-items: center;
  }

  /* 表格视图样式 */
  .table-title-cell {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    width: 100%;
  }

  .table-thumb {
    width: 60px;
    height: 40px;
    flex-shrink: 0;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table-title-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
  }

  .table-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
    line-height: 1.4;
  }

  .image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    color: #909399;
  }

  /* 视图切换和分页容器 */
  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    padding: 10px 0;
  }

  .view-toggle-top {
    margin-top: 10px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .batch-actions {
    display: flex;
    gap: 10px;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .view-toggle-top {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .batch-actions {
      margin-top: 10px;
    }
  }

  /* 封面对比样式 */
  .cover-comparison {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }

  .cover-item {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .cover-item h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #606266;
  }

  .cover-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    height: 200px;
    position: relative;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cover-container .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 抖音图标样式 */
  .douyin-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    vertical-align: -2px;
  }

  /* 标签内容样式 */
  .tag-content {
    display: flex;
    align-items: center;
    line-height: normal;
  }

  /* 视频预览样式 */
  .video-upload-preview-container {
    display: flex;
    gap: 20px;
  }

  @media (max-width: 768px) {
    .video-upload-preview-container {
      flex-direction: column;
    }

    .video-upload-container,
    .video-preview-wrapper {
      flex: none;
      width: 100%;
    }

    .video-preview-wrapper {
      margin-top: 20px;
    }
  }

  .video-upload-container {
    flex: 1;
  }

  .video-preview-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .video-preview-container {
    width: 100%;
    margin-top: 0;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
  }

  .video-preview-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
    font-weight: 500;
    text-align: center;
  }

  .video-preview {
    width: 100%;
    max-height: 300px;
    object-fit: contain;
    background-color: #000;
    border-radius: 4px;
  }

  .preview-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
  }

  .topic-search {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .topic-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .topic-stats {
    display: flex;
    gap: 8px;
  }

  .topic-stats .el-tag {
    font-size: 12px;
  }

  /* 手动输入话题对话框样式 */
  .custom-topic-input {
    padding: 0px;
  }

  .topic-input-tip {
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
  }

  .topic-preview {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f8f8f8;
  }

  .preview-label {
    font-weight: 500;
    margin-bottom: 10px;
    color: #606266;
  }

  .topic-tags-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .topic-tags-preview .topic-tag {
    margin-bottom: 4px;
  }

  /* 批量选择样式 */
  .batch-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .select-count {
    font-size: 14px;
    color: #606266;
  }

  .card-select-box {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
  }

  .promotion-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .promotion-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .promotion-links {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .table-promotion-link-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .table-promotion-link-row:hover {
    background-color: #e9ecef;
  }

  .url-link {
    color: #409eff;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    flex: 1;
  }

  .url-link:hover {
    color: #1890ff;
  }

  .no-promotion {
    color: #909399;
  }

  .duration-icon {
    margin-right: 4px;
    font-size: 12px;
    vertical-align: -1px;
  }

  .video-duration {
    font-size: 12px;
    color: #909399;
    display: inline-flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
  }

  .table-duration-under-title {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    display: flex;
    align-items: center;
  }

  .table-duration-under-title .duration-icon {
    margin-right: 4px;
    font-size: 12px;
  }

  .source-status-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
    justify-content: center;
    min-height: 60px;
    padding: 8px 0;
  }

  .source-status-cell .source-tag,
  .source-status-cell .status-tag {
    align-self: flex-start;
    min-width: 60px;
    text-align: center;
  }

  .source-status-cell .el-tag {
    margin: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  .source-icon {
    margin-right: 4px;
    font-size: 12px;
    vertical-align: -1px;
  }

  .cart-icon {
    font-size: 16px;
    color: #409eff;
    flex-shrink: 0;
  }

  .table-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }

  .table-actions .el-button {
    margin: 0;
    padding: 4px 8px;
    height: auto;
    min-height: 28px;
    display: inline-flex;
    align-items: center;
    font-size: 12px;
  }

  .table-actions .el-button .el-icon {
    margin-right: 4px;
    font-size: 12px;
  }
</style>
