<template>
  <div>
    <!-- 抖音号搜索弹窗 -->
    <el-dialog v-model="searchUniqueIdDialogVisible" title="搜索抖音号" width="520px" center draggable>
      <div class="flex flex-col items-center gap-4">
        <el-form :model="authForm" label-width="80px" class="w-full">
          <el-form-item label="账号类型">
            <el-radio-group v-model="searchUniqueIdForm.accountType">
              <el-radio :label="1">个人账号</el-radio>
              <el-radio :label="2">企业账号</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择分类">
            <el-tree-select
              v-model="searchUniqueIdForm.categoryId"
              :data="categories"
              check-strictly
              :props="defaultProps"
              :render-after-expand="false"
              style="width: 100%"
              placeholder="请选择分类"
            />
          </el-form-item>
          <el-form-item label="抖音号">
            <el-input v-model="searchUniqueIdForm.uniqueId" placeholder="请输入抖音号" />
          </el-form-item>
          <!-- 增加一列代理ip选项 -->
          <el-form-item label="代理IP">
            <el-input v-model="searchUniqueIdForm.proxy" placeholder="请获取代理IP" disabled />
            <!-- 增加两个文字按钮：1.搜索匹配 2.独立ip -->
            <el-button type="text" @click="searchProxyFit" style="font-size: 12px; text-decoration: underline">
              搜索匹配同手机ip
            </el-button>
            <el-button type="text" @click="getIndependentIp" style="font-size: 12px; text-decoration: underline">
              独立IP
            </el-button>
          </el-form-item>

          <el-form-item label="匹配代理" v-if="searchProxyFormItem">
            <!-- 在同一行增加两个元素，一个是下拉框fitProxyChannel：1-抖音号，2-代理端口。另一个是el-autocomplete组件，用于搜索匹配的代理 -->
            <el-col :span="6">
              <el-select v-model="fitProxyChannel" placeholder="请选择渠道">
                <el-option label="抖音号" value="1"></el-option>
                <el-option label="代理端口" value="2"></el-option>
                <el-option label="手机MAC地址" value="3"></el-option>
              </el-select>
            </el-col>
            <el-col :span="16">
              <el-autocomplete
                v-model="fitProxyKeyword"
                :fetch-suggestions="fitProxySearch"
                placeholder="请根据渠道输入关键信息"
                @select="handleFitProxySelect"
                :trigger-on-focus="true"
                popper-class="phone-suggestions"
                class="w-full"
              >
                <template #default="{ item }">
                  {{
                    Number(fitProxyChannel) === 1
                      ? item.nickname
                      : Number(fitProxyChannel) === 2
                      ? item.bindIP
                      : item.nickname || item.bindIP + ' (匹配MAC)'
                  }}
                </template>
              </el-autocomplete>
            </el-col>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="authGetQrCode">前往授权</el-button>
        <el-button @click="closeSearchUniqueIdDialog">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 新添加授权弹窗 -->
    <el-dialog
      v-model="getQrCodeDialogVisible"
      title="获取二维码"
      width="520px"
      center
      draggable
      @open="startPolling"
      @close="stopPolling"
    >
      <div class="flex flex-col items-center gap-4">
        <el-form :model="authForm" label-width="80px" class="w-full">
          <el-form-item label="uniqueId">
            <el-input v-model="authForm.uniqueId" disabled />
          </el-form-item>
          <el-form-item label="选择分类" disabled>
            <el-tree-select
              v-model="authForm.categoryId"
              :data="categories"
              check-strictly
              :props="defaultProps"
              :render-after-expand="false"
              style="width: 100%"
              placeholder="请选择分类"
            />
          </el-form-item>
          <el-form-item label="did">
            <el-input v-model="authForm.did" placeholder="请输入设备ID" disabled />
          </el-form-item>
          <el-form-item label="iid">
            <el-input v-model="authForm.iid" placeholder="请输入iid" disabled />
          </el-form-item>
          <el-form-item label="代理">
            <el-input v-model="authForm.proxy" placeholder="请输入代理" disabled />
          </el-form-item>
          <el-form-item label="扫码登录">
            <!-- 显示二维码图片 -->
            <img
              v-if="authForm.qr_code_url"
              :src="`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(
                authForm.qr_code_url
              )}`"
              alt="二维码"
            />
            <el-input v-model="authForm.qr_code_url" disabled v-else />
          </el-form-item>
          <div v-if="authForm.warningText" class="warning-text" style="color: orange">{{ authForm.warningText }}</div>
          <!-- 添加v-if指令来控制显示隐藏 -->
          <el-form-item label="验证码" v-if="authForm.showCaptchaInput">
            <el-input v-model="authForm.code" placeholder="请输入验证码" />
          </el-form-item>
          <el-form-item v-if="authForm.showCaptchaInput">
            <el-button @click="validSms">验证</el-button>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="closeGetQrCodeDialog">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  categories: {
    type: Array,
    required: true
  }
})

const emit = defineEmits([
  'auth-success',
  'get-independent-ip',
  'search-proxy-fit',
  'fit-proxy-search',
  'handle-fit-proxy-select'
])

const searchUniqueIdDialogVisible = ref(false)
const getQrCodeDialogVisible = ref(false)
const searchProxyFormItem = ref(false)
const fitProxyChannel = ref(null)
const fitProxyKeyword = ref(null)

const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'ID'
}

const authForm = reactive({
  categoryId: null,
  did: '',
  accountType: null,
  token: null,
  showCaptchaInput: null,
  iid: null,
  proxy: null,
  qr_code_url: null,
  code: null,
  warningText: null,
  uniqueId: null
})

const searchUniqueIdForm = reactive({
  uniqueId: null,
  accountType: null,
  categoryId: null,
  proxy: null
})

const showSearchUniqueIdDialog = () => {
  searchUniqueIdForm.uniqueId = null
  searchUniqueIdForm.accountType = 1
  searchUniqueIdForm.categoryId = null
  searchUniqueIdForm.proxy = null
  searchUniqueIdDialogVisible.value = true
}

const closeSearchUniqueIdDialog = () => {
  searchUniqueIdForm.uniqueId = null
  searchUniqueIdForm.accountType = null
  searchUniqueIdForm.categoryId = null
  searchUniqueIdForm.proxy = null
  searchUniqueIdDialogVisible.value = false
  searchProxyFormItem.value = false
}

const closeGetQrCodeDialog = () => {
  stopPolling()
  getQrCodeDialogVisible.value = false
  authForm.showCaptchaInput = false
  authForm.token = null
  authForm.did = null
  authForm.iid = null
  authForm.proxy = null
  authForm.qr_code_url = null
  authForm.code = null
  authForm.warningText = null
  authForm.accountType = null
  authForm.categoryId = null
  authForm.uniqueId = null
}

const authGetQrCode = () => {
  if (!searchUniqueIdForm.uniqueId) {
    ElMessage.error('请填写抖音号')
    return
  }
  if (!searchUniqueIdForm.categoryId && searchUniqueIdForm.categoryId != 0) {
    ElMessage.error('请选择分类')
    return
  }
  if (!searchUniqueIdForm.accountType && searchUniqueIdForm.accountType != 0) {
    ElMessage.error('请选择账号类型')
    return
  }
  if (!searchUniqueIdForm.proxy) {
    ElMessage.error('请获取合适的代理')
    return
  }
  
  // 复制数据到authForm
  Object.assign(authForm, searchUniqueIdForm)
  
  closeSearchUniqueIdDialog()
  getQrCodeDialogVisible.value = true
  
  emit('auth-success', { ...searchUniqueIdForm })
}

const getIndependentIp = () => {
  emit('get-independent-ip')
}

const searchProxyFit = () => {
  searchProxyFormItem.value = true
  fitProxyChannel.value = null
  fitProxyKeyword.value = null
  emit('search-proxy-fit')
}

const fitProxySearch = (queryString, cb) => {
  emit('fit-proxy-search', queryString, cb, fitProxyChannel.value)
}

const handleFitProxySelect = (item) => {
  searchUniqueIdForm.proxy = item.bindIP
  emit('handle-fit-proxy-select', item)
}

const validSms = () => {
  if (!authForm.code && authForm.code == '') {
    ElMessage.error('请输入验证码')
    return
  }
  emit('valid-sms', authForm.code, authForm.token)
}

const startPolling = () => {
  emit('start-polling')
}

const stopPolling = () => {
  emit('stop-polling')
}

defineExpose({
  showSearchUniqueIdDialog,
  closeGetQrCodeDialog,
  authForm,
  searchUniqueIdForm,
  getQrCodeDialogVisible,
  searchUniqueIdDialogVisible
})
</script>
