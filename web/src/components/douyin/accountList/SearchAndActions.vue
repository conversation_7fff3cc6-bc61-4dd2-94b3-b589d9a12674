<template>
  <div>
    <warning-bar :title="'账号授权请先选择分类再扫码：抖音--我--右上角菜单--我的二维码--扫一扫'" class="mb-4" />

    <!-- 搜索和操作区域 -->
    <div class="flex flex-wrap items-center gap-3 mb-4">
      <el-button type="primary" icon="Key" @click="showSearchUniqueIdDialog" class="flex-none">
        账号授权
      </el-button>
      <div class="flex-1 flex flex-wrap gap-3">
        <el-input
          v-model="searchForm.nickname"
          placeholder="请输入用户昵称"
          class="w-full sm:w-64 md:w-56 lg:w-64"
          clearable
        />
        <el-input
          v-model="searchForm.uniqueId"
          placeholder="请输入抖音号"
          class="w-full sm:w-64 md:w-56 lg:w-64"
          clearable
        />
        <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
        <el-button type="success" icon="Refresh" @click="checkAllUsersStatus">刷新</el-button>
        <el-button type="danger" icon="Refresh" @click="getInValidLoginUsers">筛选未登录</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import WarningBar from '@/components/warningBar/warningBar.vue'

const props = defineProps({
  search: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['search', 'auth-account', 'refresh-all', 'filter-invalid'])

const searchForm = reactive({
  nickname: props.search.nickname,
  uniqueId: props.search.uniqueId
})

const onSubmit = () => {
  emit('search', {
    nickname: searchForm.nickname,
    uniqueId: searchForm.uniqueId
  })
}

const showSearchUniqueIdDialog = () => {
  emit('auth-account')
}

const checkAllUsersStatus = () => {
  emit('refresh-all')
}

const getInValidLoginUsers = () => {
  emit('filter-invalid')
}
</script>
