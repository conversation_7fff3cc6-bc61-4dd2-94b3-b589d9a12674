<template>
  <div>
    <!-- 更新IP弹窗 -->
    <el-dialog v-model="updateIPDialogVisible" title="更新绑定IP" width="400px" center draggable>
      <el-form :model="updateIPForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ updateIPForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="当前IP">
          <el-tag size="small" type="info" v-if="!updateIPForm.currentIP">未绑定</el-tag>
          <span v-else>{{ updateIPForm.currentIP }}</span>
        </el-form-item>
        <el-form-item label="新IP" prop="newIP">
          <div class="flex gap-2 items-start">
            <el-select
              v-model="updateIPForm.newIP"
              placeholder="请选择或搜索新的IP地址"
              class="flex-1"
              filterable
              clearable
            >
              <el-option v-for="ip in updateIPForm.availableIPs" :key="ip" :label="ip" :value="ip" />
            </el-select>
            <el-button type="warning" size="default" @click="clearIPInput" :disabled="!updateIPForm.newIP">
              清空
            </el-button>
          </div>
          <div class="text-gray-500 text-sm mt-1">
            {{ updateIPForm.newIP ? '请选择新的IP地址' : '清空IP后确定将解绑当前IP' }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="updateIPDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateIP">
          {{ updateIPForm.newIP ? '更新' : '解绑' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 实名弹窗 -->
    <el-dialog v-model="bindRealNameDialogVisible" title="实名认证" width="400px" center draggable>
      <el-form :model="bindRealNameForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ bindRealNameForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="姓名">
          <div class="flex gap-2 items-start">
            <el-input v-model="bindRealNameForm.realName" placeholder="请输入真实姓名，留空表示清空" class="flex-1" />
            <el-button type="warning" size="default" @click="clearRealNameInput" :disabled="!bindRealNameForm.realName">
              清空
            </el-button>
          </div>
          <div class="text-gray-500 text-sm mt-1">请输入真实姓名，或点击清空按钮清除当前实名认证</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bindRealNameDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBindRealName">确定</el-button>
      </template>
    </el-dialog>

    <!-- 绑定设备弹窗 -->
    <el-dialog v-model="bindPhoneDialogVisible" title="绑定手机号" width="400px" center draggable>
      <el-form :model="bindPhoneForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ bindPhoneForm.nickname }}</span>
        </el-form-item>
        <el-form-item label="手机号">
          <div class="flex gap-2 items-start">
            <el-autocomplete
              v-model="bindPhoneForm.phone"
              :fetch-suggestions="phoneSearch"
              placeholder="请输入手机号搜索，留空表示解绑"
              @select="phoneSelected"
              :trigger-on-focus="true"
              popper-class="phone-suggestions"
              class="flex-1"
            >
              <template #default="{ item }">
                <div class="flex flex-col">
                  <span>{{ item.phoneNumber }}</span>
                  <span class="text-gray-500 text-sm">
                    {{ item.operatorType === 'mobile' ? '移动' : item.operatorType === 'unicom' ? '联通' : '电信' }} -
                    {{ item.realName || '未实名' }}
                  </span>
                </div>
              </template>
            </el-autocomplete>
            <el-button type="warning" size="default" @click="clearPhoneInput" :disabled="!bindPhoneForm.phone">
              清空
            </el-button>
          </div>
          <div class="text-gray-500 text-sm mt-1">
            {{ bindPhoneForm.phone ? '请从下拉列表中选择手机号' : '清空手机号后确定将解绑当前手机号' }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bindPhoneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBindPhone">
          {{ bindPhoneForm.phone ? '绑定' : '解绑' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 更新分类弹窗 -->
    <el-dialog v-model="updateCategoryDialogVisible" title="更新分类" width="400px" center draggable>
      <el-form :model="categoryFormData">
        <el-form-item label="选择分类">
          <el-select v-model="categoryFormData.categoryId" placeholder="请选择分类" style="width: 100%">
            <el-option
              v-for="category in flattenCategories"
              :key="category.ID"
              :label="category.name"
              :value="category.ID"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="updateCategoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateCategory">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加备注弹窗 -->
    <el-dialog v-model="remarkDialogVisible" title="账号备注" width="400px" center draggable>
      <div class="p-4">
        <el-form :model="remarkForm" label-width="80px">
          <el-form-item label="账号">
            <span>{{ remarkForm.nickname }}</span>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="remarkForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="remarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateRemark">确定</el-button>
      </template>
    </el-dialog>

    <!-- 账号转移弹窗 -->
    <el-dialog v-model="transferDialogVisible" title="账号转移" width="500px" center draggable>
      <div class="transfer-content">
        <div class="transfer-info mb-4">
          <el-alert
            title="转移说明"
            type="info"
            show-icon
            :closable="false"
            description="转移后，该账号及其绑定的手机号归属将转移至选择的系统用户下，转移的账号将自动排在新用户的账号列表首位。"
          />
        </div>

        <el-form :model="transferForm" label-width="100px">
          <el-form-item label="转移账号">
            <div class="flex items-center gap-2">
              <el-avatar :size="40" :src="getAvatarUrl(transferForm.avatar)" />
              <div>
                <div class="font-medium">{{ transferForm.nickname }}</div>
                <div class="text-sm text-gray-500">{{ transferForm.uniqueId }}</div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="当前归属">
            <span>{{ transferForm.sysUserName || '未知用户' }}</span>
          </el-form-item>

          <el-form-item label="绑定手机">
            <span v-if="transferForm.bindPhone">
              {{ transferForm.bindPhone }}
              <el-tag size="small" type="info" class="ml-2">
                {{ transferForm.phoneOperator }}-{{ transferForm.phoneUserName || '未实名' }}
              </el-tag>
            </span>
            <span v-else class="text-gray-500">未绑定</span>
          </el-form-item>

          <el-form-item label="转移到" required>
            <el-select
              v-model="transferForm.targetUserId"
              placeholder="请选择目标系统用户"
              style="width: 100%"
              filterable
              loading-text="加载中..."
            >
              <el-option
                v-for="user in systemUserList"
                :key="user.id"
                :label="user.nickName || user.userName"
                :value="user.id"
                :disabled="user.id === transferForm.currentUserId"
              >
                <div class="flex justify-between">
                  <span>{{ user.nickName || user.userName }}</span>
                  <span class="text-gray-400" v-if="user.id === transferForm.currentUserId">(当前用户)</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="transferDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmTransfer"
          :disabled="!transferForm.targetUserId"
          :loading="transferLoading"
        >
          确定转移
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
  flattenCategories: {
    type: Array,
    default: () => []
  },
  systemUserList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'update-ip',
  'bind-real-name',
  'bind-phone',
  'update-category',
  'update-remark',
  'transfer-user',
  'phone-search'
])

// 更新IP相关
const updateIPDialogVisible = ref(false)
const updateIPForm = reactive({
  id: null,
  nickname: '',
  currentIP: '',
  newIP: '',
  availableIPs: []
})

// 实名相关
const bindRealNameDialogVisible = ref(false)
const bindRealNameForm = reactive({
  id: null,
  nickname: '',
  realName: ''
})

// 绑定设备相关
const bindPhoneDialogVisible = ref(false)
const bindPhoneForm = reactive({
  id: null,
  nickname: '',
  phone: '',
  selectedPhone: null
})

// 更新分类相关
const updateCategoryDialogVisible = ref(false)
const categoryFormData = reactive({
  ID: null,
  categoryId: null
})

// 备注相关
const remarkDialogVisible = ref(false)
const remarkForm = reactive({
  id: null,
  nickname: '',
  remark: ''
})

// 账号转移相关
const transferDialogVisible = ref(false)
const transferLoading = ref(false)
const transferForm = reactive({
  id: null,
  nickname: '',
  uniqueId: '',
  avatar: '',
  sysUserName: '',
  bindPhone: '',
  phoneOperator: '',
  phoneUserName: '',
  currentUserId: null,
  targetUserId: null
})

const showUpdateIPDialog = (row, availableIPs = []) => {
  Object.assign(updateIPForm, {
    id: row.ID,
    nickname: row.nickname,
    currentIP: row.bindIP || '',
    newIP: '',
    availableIPs
  })
  if (availableIPs.length > 0) {
    updateIPForm.newIP = availableIPs[0]
  }
  updateIPDialogVisible.value = true
}

const showBindRealNameDialog = (row) => {
  Object.assign(bindRealNameForm, {
    id: row.ID,
    nickname: row.nickname,
    realName: row.realName
  })
  bindRealNameDialogVisible.value = true
}

const showBindPhoneDialog = (row) => {
  Object.assign(bindPhoneForm, {
    id: row.ID,
    nickname: row.nickname,
    phone: row.bindPhone || '',
    selectedPhone: null
  })
  bindPhoneDialogVisible.value = true
}

const showUpdateCategoryDialog = (row) => {
  Object.assign(categoryFormData, {
    ID: row.ID,
    categoryId: row.categoryId
  })
  updateCategoryDialogVisible.value = true
}

const showRemarkDialog = (row) => {
  Object.assign(remarkForm, {
    id: row.ID,
    nickname: row.nickname,
    remark: row.remark || ''
  })
  remarkDialogVisible.value = true
}

const showTransferDialog = (row) => {
  Object.assign(transferForm, {
    id: row.ID,
    nickname: row.nickname,
    uniqueId: row.uniqueId,
    avatar: row.avatar,
    sysUserName: row.sysUserName,
    bindPhone: row.bindPhone,
    phoneOperator: row.phoneOperator,
    phoneUserName: row.phoneUserName,
    currentUserId: row.sysUserId,
    targetUserId: null
  })
  transferDialogVisible.value = true
}

const clearIPInput = () => {
  updateIPForm.newIP = ''
}

const clearRealNameInput = () => {
  bindRealNameForm.realName = ''
}

const clearPhoneInput = () => {
  bindPhoneForm.phone = ''
  bindPhoneForm.selectedPhone = null
}

const confirmUpdateIP = () => {
  emit('update-ip', updateIPForm)
}

const confirmBindRealName = () => {
  emit('bind-real-name', bindRealNameForm)
}

const confirmBindPhone = () => {
  emit('bind-phone', bindPhoneForm)
}

const confirmUpdateCategory = () => {
  emit('update-category', categoryFormData)
}

const confirmUpdateRemark = () => {
  emit('update-remark', remarkForm)
}

const confirmTransfer = () => {
  emit('transfer-user', transferForm)
}

const phoneSearch = (phone, cb) => {
  emit('phone-search', phone, cb)
}

const phoneSelected = (item) => {
  bindPhoneForm.phone = item.phoneNumber
  bindPhoneForm.selectedPhone = item.phoneNumber
}

const getAvatarUrl = (avatarJson) => {
  try {
    if (!avatarJson) return ''
    const avatarData = JSON.parse(avatarJson)
    return avatarData.url_list?.[0] || ''
  } catch (err) {
    console.error('解析头像JSON失败:', err)
    return ''
  }
}

defineExpose({
  showUpdateIPDialog,
  showBindRealNameDialog,
  showBindPhoneDialog,
  showUpdateCategoryDialog,
  showRemarkDialog,
  showTransferDialog,
  updateIPDialogVisible,
  bindRealNameDialogVisible,
  bindPhoneDialogVisible,
  updateCategoryDialogVisible,
  remarkDialogVisible,
  transferDialogVisible,
  transferLoading
})
</script>

<style scoped>
/* 账号转移弹窗样式 */
.transfer-content {
  padding: 16px;
}

.transfer-info {
  margin-bottom: 20px;
}
</style>
