<template>
  <!-- 添加绑定信息弹窗 -->
  <el-dialog v-model="bindInfoDialogVisible" title="绑定信息" width="800px" center draggable>
    <div class="bind-info-container">
      <!-- 基础信息模块 -->
      <el-card shadow="hover" class="info-section mb-4">
        <template #header>
          <div class="section-header">
            <h4>基础信息</h4>
          </div>
        </template>
        <div class="info-content">
          <!-- 序号/WIFI信息 -->
          <div class="info-item-compact">
            <span class="info-label">序号/WIFI:</span>
            <span class="info-value">{{ currentBindInfo.ipSort || '未知' }}</span>
          </div>

          <!-- 代理IP信息 -->
          <div class="info-item-compact">
            <span class="info-label">代理IP:</span>
            <div class="flex items-center gap-2 flex-1">
              <span class="ip-address">{{ currentBindInfo.bindIP || '未绑定' }}</span>
              <el-tag
                v-if="currentBindInfo.bindIP && currentBindInfo.ipHealthStatus"
                :type="getIPHealthStatusType(currentBindInfo.ipHealthStatus)"
                size="small"
              >
                {{ getIPHealthStatusText(currentBindInfo.ipHealthStatus) }}
              </el-tag>
              <!-- 操作按钮直接放在IP旁边 -->
              <div class="flex gap-1 ml-auto">
                <el-button size="small" type="primary" @click="showUpdateIPDialog(currentBindInfo)">
                  {{ currentBindInfo.bindIP ? '更换' : '绑定' }}
                </el-button>
                <el-button
                  v-if="currentBindInfo.bindIP"
                  size="small"
                  type="success"
                  @click="checkUserIPHealth(currentBindInfo)"
                >
                  检查健康
                </el-button>
              </div>
            </div>
          </div>

          <!-- 设备信息 -->
          <div class="info-item-compact">
            <span class="info-label">设备ID (DID):</span>
            <span class="info-value">{{ currentBindInfo.did || '未配置' }}</span>
          </div>
          <div class="info-item-compact">
            <span class="info-label">实例ID (IID):</span>
            <span class="info-value">{{ currentBindInfo.iid || '未配置' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 同IP用户列表 -->
      <el-card shadow="hover" class="info-section" v-if="currentBindInfo.bindIP">
        <template #header>
          <div class="section-header">
            <h4>同IP用户列表</h4>
            <el-button size="small" type="primary" @click="refreshSameIPUsers" :loading="sameIPUsersLoading">
              刷新
            </el-button>
          </div>
        </template>
        <div class="same-ip-users">
          <el-tabs v-model="activeUserTab">
            <el-tab-pane label="抖音用户" name="douyin">
              <el-table :data="sameIPUsers.dyUsers" border size="small" v-if="sameIPUsers.dyUsers.length > 0">
                <el-table-column label="昵称" min-width="100">
                  <template #default="scope">
                    <div class="flex flex-col">
                      <div class="font-medium">{{ scope.row.nickname }}</div>
                      <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                      <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                        {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="uniqueId" label="抖音号" min-width="120" />
                <el-table-column label="备注" min-width="150">
                  <template #default="scope">
                    <div class="whitespace-pre-line text-sm">
                      {{ scope.row.remark || '无备注' }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无抖音用户" :image-size="80" />
            </el-tab-pane>
            <el-tab-pane label="火山版用户" name="flame">
              <el-table :data="sameIPUsers.flameUsers" border size="small" v-if="sameIPUsers.flameUsers.length > 0">
                <el-table-column label="昵称" min-width="100">
                  <template #default="scope">
                    <div class="flex flex-col">
                      <div class="font-medium">{{ scope.row.nickname }}</div>
                      <div class="text-sm text-gray-500">{{ scope.row.bindPhone || '未绑定' }}</div>
                      <div v-if="scope.row.bindPhone" class="text-xs text-gray-400">
                        {{ scope.row.phoneOperator }}-{{ scope.row.phoneUserName || '未实名' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="isCollectEnabled" label="采集状态" min-width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.isCollectEnabled ? 'success' : 'info'" size="small">
                      {{ scope.row.isCollectEnabled ? '已开启' : '未开启' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无火山版用户" :image-size="80" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'

const emit = defineEmits(['update-ip', 'check-ip-health', 'refresh-same-ip-users'])

const bindInfoDialogVisible = ref(false)
const currentBindInfo = reactive({
  bindIP: '',
  did: '',
  iid: '',
  ID: null,
  nickname: '',
  ipSort: null,
  ipId: null,
  ipHealthStatus: null
})

// 同IP用户相关
const sameIPUsers = reactive({
  dyUsers: [],
  flameUsers: []
})
const sameIPUsersLoading = ref(false)
const activeUserTab = ref('douyin')

const showBindInfoDialog = (row) => {
  Object.assign(currentBindInfo, row)
  bindInfoDialogVisible.value = true
}

const showUpdateIPDialog = (row) => {
  emit('update-ip', row)
}

const checkUserIPHealth = (user) => {
  emit('check-ip-health', user)
}

const refreshSameIPUsers = () => {
  if (!currentBindInfo.ipId) return
  
  sameIPUsersLoading.value = true
  emit('refresh-same-ip-users', currentBindInfo.ipId, (users) => {
    Object.assign(sameIPUsers, users)
    sameIPUsersLoading.value = false
  })
}

// IP健康状态相关工具方法
const getIPHealthStatusType = (status) => {
  switch (status) {
    case 'healthy':
      return 'success'
    case 'unhealthy':
      return 'danger'
    default:
      return 'info'
  }
}

const getIPHealthStatusText = (status) => {
  switch (status) {
    case 'healthy':
      return '健康'
    case 'unhealthy':
      return '不健康'
    default:
      return '未检查'
  }
}

defineExpose({
  showBindInfoDialog,
  bindInfoDialogVisible,
  currentBindInfo,
  sameIPUsers,
  sameIPUsersLoading
})
</script>

<style scoped>
/* 绑定信息弹窗样式 */
.bind-info-container {
  padding: 16px;
  max-height: 75vh;
  overflow-y: auto;
}

.info-section {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.info-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.info-content {
  padding: 16px;
}

.info-item-compact {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.info-label {
  font-weight: 600;
  color: #606266;
  min-width: 120px;
  margin-right: 12px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.ip-address {
  font-family: 'Monaco', 'Consolas', monospace;
  background: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  color: #409eff;
  font-weight: 600;
}

.same-ip-users {
  padding: 16px 0;
}

.same-ip-users .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.same-ip-users .el-table th {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

.same-ip-users .el-empty {
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bind-info-container {
    padding: 12px;
  }

  .info-item-compact {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    min-width: auto;
    margin-bottom: 4px;
  }
}
</style>
