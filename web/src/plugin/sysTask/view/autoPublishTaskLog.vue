<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="form" class="demo-form-inline">
      <el-form-item label="抖音号">
        <el-input v-model="form.uniqueId" placeholder="请输入抖音号" clearable />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="form.status" placeholder="请选择状态" clearable>
          <el-option 
            v-for="status in [1,2,3,4,5,6,7]" 
            :key="status" 
            :label="statusText(status)" 
            :value="status.toString()" 
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
    >
    <el-table-column prop="ID" label="id" width="120" />
      <el-table-column prop="CreatedAt" label="创建时间" width="180">
        <template #default="{ row }">
          {{ new Date(row.CreatedAt).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }).replace(/\//g, '-') }}
        </template>
      </el-table-column>
      <el-table-column prop="UpdatedAt" label="更新时间" width="180">
        <template #default="{ row }">
          {{ new Date(row.UpdatedAt).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }).replace(/\//g, '-') }}
        </template>
      </el-table-column>
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column prop="uniqueId" label="抖音号" />
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <span :style="{ color: [4, 5, 6].includes(row.status) ? 'red' : '' }">
            {{ statusText(row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="重试" width="160">
        <template #default="{ row }">
          <div v-if="row.retryTime" class="retry-info">
            <span class="text-xs">重试次数: {{ row.retryCount || 0 }}</span>
            <br>
            <span class="text-xs">重试方式: {{ row.retryMethod === 1 ? '手动' : row.retryMethod === 2 ? '自动' : '无' }}</span>
            <br>
            <span class="text-xs">重试时间:{{ formatTimestamp(row.retryTime)}} </span>
          </div>
        </template>
      </el-table-column>
      <!-- 新增操作列 -->
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleView(row)">详情</el-button>
          <el-button v-if="[4, 5, 6].includes(row.status)" type="warning" size="small" @click="handleRetry(row)">重发</el-button>
        </template>
      </el-table-column>
    </el-table>
        <div class="gva-pagination">
      <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>

    <!-- 将对话框移到app-container内部 -->
    <el-dialog v-model="dialogVisible" title="任务详情" width="60%">
      <el-form label-width="120px">
        <el-form-item label="ID">
          <el-input v-model="currentRow.ID" :disabled="true" />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-input :value="formatDate(currentRow.CreatedAt)" :disabled="true" />
        </el-form-item>
        <el-form-item label="更新时间">
          <el-input :value="formatDate(currentRow.UpdatedAt)" :disabled="true" />
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="currentRow.nickname" :disabled="true" />
        </el-form-item>
        <el-form-item label="抖音号">
          <el-input v-model="currentRow.uniqueId" :disabled="true" />
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="currentRow.title" :disabled="true" />
        </el-form-item>
        <el-form-item label="任务id">
          <el-input v-model="currentRow.taskId" :disabled="true" />
        </el-form-item>
        <el-form-item label="状态">
          <el-input :value="statusText(currentRow.status)" :disabled="true" />
        </el-form-item>
        <el-form-item label="失败原因">
          <el-input
            v-model="currentRow.reason"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            :disabled="true"
            placeholder="若失败时，此处会显示失败原因。"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
  import { formatTimestamp } from '@/utils/format'

import { getAutoPublishVideoLogList, republishVideo } from '@/api/creative/autoPublishVideo'

const form = ref({
  uniqueId: '',
  status: ''
})
const tableData = ref([])
const listLoading = ref(false)
const page = ref(1)
const pageSize = ref(30)
const total = ref(0)

const getTableData = async () => {
  listLoading.value = true
    try {
        const params = {
            page: page.value,
            pageSize: pageSize.value,
        }
        if (form.value.status)
            params.status = form.value.status
        if (form.value.uniqueId)
            params.uniqueId = form.value.uniqueId
        
        const res = await getAutoPublishVideoLogList(params)
        if (res.code != 0) {
            ElMessage.error('查询失败:', res.msg);
            return;
        }
        tableData.value = res.data?.list || []
        total.value = res.data?.total
    } catch (err) { 
        ElMessage.error('查询异常:', err.message || '未知错误');
        return;
    }
    finally {
    listLoading.value = false
  }
}
getTableData();

const handleSearch = () => {
  page.value = 1
  getTableData()
}

const handleReset = () => {
  form.value = {
    uniqueId: '',
    status: ''
  }
}

const statusText = (status) => {
  switch (status) {
    case 1:
      return '上传中'
    case 2:
      return '上传完成'
    case 3:
      return '发布成功'
    case 4:
      return '发布失败'
    case 5:
      return '上传失败'
    case 6:
          return '查询上传结果失败'
    case 7:
      return '需要短信验证'
    default:
  }
}

const dialogVisible = ref(false)
const currentRow = ref({})

// 日期格式化方法复用表格中的逻辑
const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit', 
    hour12: false 
  }).replace(/\//g, '-')
}

const handleView = (row) => {
  currentRow.value = { ...row }
  dialogVisible.value = true
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val;
  getTableData();
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  getTableData();
}

  const  handleRetry = async (row) =>  {
      try {
        // 弹出确认框
        const confirm = await ElMessageBox.confirm(
          `是否重新发布任务: ${row.title}`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        if (confirm !== 'confirm') {
          return;
        }

        ElMessage.warning('重新发布中，请稍后...');
        const res = await republishVideo({
          recordId: row.ID
        })
        if (res.code != 0) {
          ElMessage.error('重新发布失败:', res.msg);
          getTableData();
          return;
        }

        ElMessage.success('重新发布成功');
        getTableData();
        return;
      }catch (err) {
          ElMessage.error('重新发布异常:', err.message || '未知错误');
          return;
      }
    }

</script>

<style scoped>
.app-container {
  padding: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

  .retry-info  {
    margin-top: 4px;
    padding: 4px 6px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #e9ecef;
  }

  .retry-info div {
    line-height: 1.3;
  }
</style>